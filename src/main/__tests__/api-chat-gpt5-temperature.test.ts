import { APIRouteHandlers } from "../../main/api-route-handlers";

// Loose stubs for ctor deps
const dbStub: any = {
  getPreference: async (key: string) => {
    // Mock GPT-5 model selection
    if (key === 'agent.defaultModel') return 'gpt-5';
    if (key === 'agent.temperature') return 0.7;
    return null;
  },
  getWorkspace: async () => null,
  upsertChatSession: async () => {},
  insertUsageSummary: async () => {},
  insertToolExecution: async () => {},
};

const previewProxyStub: any = {};
const previewControllerStub: any = {};

// Mock ai SDK and openai
jest.mock("ai", () => {
  const fn = jest.fn();
  return {
    streamText: fn,
    convertToModelMessages: jest.fn((msgs: any) => msgs),
    consumeStream: jest.fn(),
    tool: (def: any) => def,
    jsonSchema: (schema: any) => ({ jsonSchema: schema, validate: async (v: any) => ({ success: true, value: v }) }),
  };
});

jest.mock("@ai-sdk/openai", () => ({ openai: () => ({ id: "test-model" }) }));
// Mock broadcast helper to avoid import.meta parsing under Jest
jest.mock("../../main/broadcast-helper", () => ({
  broadcastToRenderers: jest.fn(),
  broadcastWorkspaceUpdated: jest.fn(),
}));

describe("handleChat GPT-5 temperature support", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("includes temperature parameter for GPT-5 models", async () => {
    const handlers = new APIRouteHandlers(dbStub, previewProxyStub, previewControllerStub as any);
    const req: any = { 
      body: { messages: [{ role: 'user', content: [{ type: 'text', text: 'Hello' }] }], context: undefined }, 
      on: jest.fn() 
    };
    const res: any = { 
      status: jest.fn(() => res), 
      json: jest.fn(() => res), 
      end: jest.fn(), 
      on: jest.fn(),
      setHeader: jest.fn(),
    };

    const { streamText } = require("ai");
    (streamText as jest.Mock).mockImplementationOnce((params: any) => {
      // Verify that temperature is included for GPT-5
      expect(params.temperature).toBe(0.7);
      return {
        pipeUIMessageStreamToResponse: (resp: any) => resp.status(200).end(),
      };
    });

    await handlers.handleChat(req, res);

    expect(streamText).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(200);
    // Verify no temperature warning header was set
    expect(res.setHeader).not.toHaveBeenCalledWith('X-Pasteflow-Warning', 'temperature-ignored');
  });

  it("omits temperature parameter for o1 reasoning models", async () => {
    // Mock o1 model selection
    const dbStubO1: any = {
      getPreference: async (key: string) => {
        if (key === 'agent.defaultModel') return 'o1-preview';
        if (key === 'agent.temperature') return 0.7;
        return null;
      },
      getWorkspace: async () => null,
      upsertChatSession: async () => {},
      insertUsageSummary: async () => {},
      insertToolExecution: async () => {},
    };

    const handlers = new APIRouteHandlers(dbStubO1, previewProxyStub, previewControllerStub as any);
    const req: any = { 
      body: { messages: [{ role: 'user', content: [{ type: 'text', text: 'Hello' }] }], context: undefined }, 
      on: jest.fn() 
    };
    const res: any = { 
      status: jest.fn(() => res), 
      json: jest.fn(() => res), 
      end: jest.fn(), 
      on: jest.fn(),
      setHeader: jest.fn(),
    };

    const { streamText } = require("ai");
    (streamText as jest.Mock).mockImplementationOnce((params: any) => {
      // Verify that temperature is omitted for o1 models
      expect(params.temperature).toBeUndefined();
      return {
        pipeUIMessageStreamToResponse: (resp: any) => resp.status(200).end(),
      };
    });

    await handlers.handleChat(req, res);

    expect(streamText).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(200);
    // Verify temperature warning header was set
    expect(res.setHeader).toHaveBeenCalledWith('X-Pasteflow-Warning', 'temperature-ignored');
    expect(res.setHeader).toHaveBeenCalledWith('X-Pasteflow-Warning-Message', 'The temperature setting is not supported for this reasoning model (o1/o3 series) and was ignored.');
  });

  it("includes temperature parameter for GPT-5 chat models", async () => {
    // Mock GPT-5 chat model selection
    const dbStubGpt5Chat: any = {
      getPreference: async (key: string) => {
        if (key === 'agent.defaultModel') return 'gpt-5-chat-latest';
        if (key === 'agent.temperature') return 0.5;
        return null;
      },
      getWorkspace: async () => null,
      upsertChatSession: async () => {},
      insertUsageSummary: async () => {},
      insertToolExecution: async () => {},
    };

    const handlers = new APIRouteHandlers(dbStubGpt5Chat, previewProxyStub, previewControllerStub as any);
    const req: any = { 
      body: { messages: [{ role: 'user', content: [{ type: 'text', text: 'Hello' }] }], context: undefined }, 
      on: jest.fn() 
    };
    const res: any = { 
      status: jest.fn(() => res), 
      json: jest.fn(() => res), 
      end: jest.fn(), 
      on: jest.fn(),
      setHeader: jest.fn(),
    };

    const { streamText } = require("ai");
    (streamText as jest.Mock).mockImplementationOnce((params: any) => {
      // Verify that temperature is included for GPT-5 chat models
      expect(params.temperature).toBe(0.5);
      return {
        pipeUIMessageStreamToResponse: (resp: any) => resp.status(200).end(),
      };
    });

    await handlers.handleChat(req, res);

    expect(streamText).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(200);
    // Verify no temperature warning header was set
    expect(res.setHeader).not.toHaveBeenCalledWith('X-Pasteflow-Warning', 'temperature-ignored');
  });
});
