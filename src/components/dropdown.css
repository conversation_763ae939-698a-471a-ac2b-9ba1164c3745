/* Dropdown component styles */

.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-button,
.dropdown-header {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  min-height: 28px;
  
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: var(--dropdown-font-size-base);
  font-weight: var(--dropdown-font-regular);
  letter-spacing: var(--dropdown-letter-spacing-base);
  color: var(--text-primary);
  
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  
  cursor: pointer;
  transition: background-color 0.15s ease, border-color 0.15s ease;
  position: relative;
  overflow: hidden;
}

.dropdown-button:hover,
.dropdown-header:hover {
  background-color: var(--hover-color);
  border-color: var(--border-color);
}

.dropdown-button:active,
.dropdown-header:active {
  background-color: var(--background-selected);
}

.dropdown-button:focus-visible,
.dropdown-header:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Minimal variant: borderless, tight, subtle hover */
.dropdown-minimal {
  background: transparent !important;
  border: 0 !important;
  box-shadow: none !important;
  padding: 0 6px !important;
}

.dropdown-minimal:hover {
  background: var(--background-primary) !important;
}

.dropdown-button svg,
.dropdown-header svg {
  width: 14px;
  height: 14px;
  transition: transform 0.2s ease;
  stroke-width: 1.5px;
}

.dropdown-button[aria-expanded="true"] svg,
.dropdown-header[aria-expanded="true"] svg {
  transform: rotate(180deg);
}

.dark-mode .dropdown-button:hover,
.dark-mode .dropdown-header:hover {
  background: rgba(60, 60, 60, 0.9);
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  min-width: 200px;
  max-width: 320px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  
  animation: dropdown-appear 0.15s ease-out;
  transform-origin: top left;
  
  padding: 4px;
  z-index: 1000;
}

/* Dropup variant: place menu above the button */
.dropdown-menu.dropup {
  top: auto;
  bottom: calc(100% + 4px);
  transform-origin: bottom left;
}

/* Ensure workspace dropdown menu appears above other dropdowns */
.workspace-dropdown .dropdown-menu {
  z-index: 1100;
}

.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
  margin: 4px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.dark-mode .dropdown-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark-mode .dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

@keyframes checkmark-appear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin: 2px 0;
  min-height: 30px;
  
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: var(--dropdown-font-size-base);
  font-weight: var(--dropdown-font-regular);
  line-height: var(--dropdown-line-height-base);
  color: var(--text-primary);
  
  background: transparent;
  border-radius: 4px;
  
  cursor: pointer;
  transition: background-color 0.15s ease;
  position: relative;
  user-select: none;
}

.dropdown-item:hover {
  background-color: var(--hover-color);
  color: var(--text-primary);
}

.dropdown-item.active {
  background-color: var(--background-selected);
  color: var(--text-primary);
  font-weight: var(--dropdown-font-medium);
}

.dropdown-item:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: -2px;
}

.dropdown-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  color: var(--text-secondary);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
  font-size: 11px;
  opacity: 0.7;
}

.dropdown-item.active .dropdown-item-icon {
  color: var(--accent-blue);
  opacity: 1;
}

.dropdown-item .checkmark {
  margin-left: auto;
  font-size: 13px;
  color: var(--accent-blue);
  opacity: 0.8;
  animation: checkmark-appear 0.15s ease-out;
}

.dropdown-divider {
  height: 1px;
  margin: 4px 8px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 20%,
    rgba(0, 0, 0, 0.1) 80%,
    transparent 100%
  );
}

.dark-mode .dropdown-divider {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.1) 80%,
    transparent 100%
  );
}

/* Special sort indicator style (used in sidebar) */
.dropdown-item.active .sort-indicator {
  color: var(--accent-blue);
  opacity: 1;
}

@media (prefers-reduced-motion: reduce) {
  .dropdown-menu {
    animation: none;
  }
  
  .dropdown-item {
    transition: background-color 0.1s ease;
  }
  
  .dropdown-item .checkmark {
    animation: none;
  }
}

.workspace-dropdown {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}
