# Phase 4 Implementation Plan — Advanced Features (Templates, Sessions, Security, IPC)

Executive Summary:
Phase 4 delivers advanced agent capabilities centered on code generation templates, durable chat sessions with export, a dedicated security manager, and agent-focused IPC bridges. This plan extracts the Phase 4 scope from the audit and master plan, cross-references the current codebase, and lays out a concrete, sequenced path with deliverables, affected files, technical specs, testing, and risks.

References:
- Phase 4 scope (audit): docs/vercel-ai-sdk-agent-integration-audit.md:255
- Phase 4 details (plan): docs/vercel-ai-sdk-agent-integration.md:1386, docs/vercel-ai-sdk-agent-integration.md:1916, docs/vercel-ai-sdk-agent-integration.md:1928, docs/vercel-ai-sdk-agent-integration.md:1950
- Current chat route: src/main/api-route-handlers.ts:192
- Current tools registry (Phase 3): src/main/agent/tools.ts:1
- Preload bridge (current IPC surface): src/main/preload.ts:1
- Main process (IPC, server wiring): src/main/main.ts:1
- Database implementation: src/main/db/database-implementation.ts:1
- Database bridge: src/main/db/database-bridge.ts:1
- Agent panel (renderer): src/components/agent-panel.tsx:1
- Tool call UI (renderer): src/components/agent-tool-calls.tsx:1


## 1) Phase 4 Requirements (from audit and plan)

- 4.1 Code Generation Templates
  - Implement `generateFromTemplate` tool with support for: component, hook, api-route, test
  - Follow PasteFlow conventions (kebab-case files, PascalCase exports, tests in `__tests__`)
  - Return { files[], preview, tokenCount } without writing; apply is gated via Phase 4 write path
  - Source: docs/vercel-ai-sdk-agent-integration.md:1386-1418

- 4.2 Session Management & Export
  - Persist agent chat sessions (messages, tool executions, usage summaries)
  - Add export path: UI button + IPC `agent:export-session` and CLI `export-session`
  - Source: docs/vercel-ai-sdk-agent-integration.md:1950-1978

- 4.3 Security Manager
  - Add `AgentSecurityManager` to enforce workspace file access, command sandboxing, and basic rate limits
  - Source: docs/vercel-ai-sdk-agent-integration.md:1928-1948

- 4.4 IPC Bridge Extensions
  - Add agent-specific channels: `agent:start-session`, `agent:send-message`, `agent:execute-tool`, `agent:get-history`, `agent:export-session`
  - Source: docs/vercel-ai-sdk-agent-integration.md:1916-1925

- Supporting requirements from audit
  - Missing dependencies for advanced features: diff/unified-diff; terminal deps are tracked under Phase 3/terminal work
    - Source: docs/vercel-ai-sdk-agent-integration-audit.md:384
  - Configuration surface (`AgentConfig`) and feature flags
    - Source: docs/vercel-ai-sdk-agent-integration.md:2498-2516; docs/vercel-ai-sdk-agent-integration-audit.md:371-373
  - Testing strategy additions for tools, chat flow, and diff application
    - Source: docs/vercel-ai-sdk-agent-integration.md:2519-2553


## 2) Current State Cross‑Check (codebase)

- Tools are read-only/minimal; apply is gated for Phase 4
  - src/main/agent/tools.ts:1 (read file + ripgrep + diff preview; returns "Apply requires approval")
- No agent IPC channels exist yet
  - src/main/preload.ts:1 (general wrappers only); src/main/main.ts:1 (no `agent:*` handlers)
- No session tables or persistence in DB
  - src/main/db/database-implementation.ts:1 (workspaces, preferences, instructions only)
  - src/main/api-route-handlers.ts:192 (chat streaming; no session storage/telemetry)
- CLI has no `export-session` command
  - cli/src/index.ts:1 (workspaces, prefs, preview, etc. only)
- Renderer UI lacks ModelSelector/TokenCounter/MiniFileList (Phase 3 scope); tool call UI is basic
  - src/components/agent-panel.tsx:1; src/components/agent-tool-calls.tsx:1
- Missing dependencies
  - package.json:1 (no `diff`, `unified-diff`, `xterm`, `node-pty`)

Conclusion: All Phase 4 features are net-new with integration points in tools, chat route, DB, IPC, and CLI. No conflicting code paths exist; work can be staged safely behind feature flags.


## 3) Detailed Implementation Plan

### 3.0 Preflight and Guardrails

Deliverables:
- Add minimal config surface and feature flags for Phase 4 work
- Ensure all new write operations and exports are gated by security + approval flags

Changes:
- Add `AgentConfig` type + accessor
  - File: src/main/agent/config.ts (new)
  - Reads from preferences first, then env; provides defaults
  - Keys: `DEFAULT_MODEL`, `MAX_CONTEXT_TOKENS`, `MAX_OUTPUT_TOKENS`, `MAX_TOOLS_PER_TURN`, `ENABLE_FILE_WRITE`, `ENABLE_CODE_EXECUTION`, `APPROVAL_MODE`
  - Reference: docs/vercel-ai-sdk-agent-integration.md:2498
- Add feature flags consumed in main/renderer (no UI switches required yet)
  - Window-global flags already used (AGENT_TOOL_CALLS_UI). Extend with `AGENT_ALLOW_FILE_WRITE`, `AGENT_APPROVAL_MODE`, `AGENT_ALLOW_TERMINAL` as read-only hints for renderer if needed

Validation:
- Unit: config returns sane defaults if prefs/env are missing


### 3.1 Security Manager (AgentSecurityManager)

Deliverables:
- Central `AgentSecurityManager` with:
  - validateFileAccess(path)
  - validateCommand(command)
  - checkRateLimit(sessionId)

Changes:
- New: src/main/agent/security-manager.ts
  - File validations via getPathValidator
  - Command checks (block destructive patterns) per docs example
  - Simple in-memory token bucket for per-session rate limiting
- Integrate into tools + chat route
  - src/main/agent/tools.ts:1 — consult security manager for file operations before read/write/apply
  - src/main/api-route-handlers.ts:192 — guard tool execution and message volume per session

Validation:
- Unit: simulate path escapes and malicious commands and expect denials
- Integration: chat route rejects over-limit sessions with 429


### 3.2 Session Management & Persistence

Deliverables:
- Durable session records, per-turn usage, and tool execution logs
- Backfill hooks in chat stream to record start/finish/usage

Schema (SQLite):
- Extend DB schema (id is TEXT UUID unless noted):
  - chat_sessions(id TEXT PRIMARY KEY, workspace_id TEXT, messages TEXT, created_at INTEGER, updated_at INTEGER)
  - tool_executions(id TEXT PRIMARY KEY, session_id TEXT, turn_id TEXT, tool_name TEXT, args TEXT, result TEXT, duration_ms INTEGER, timestamp INTEGER, error TEXT)
  - usage_summary(turn_id TEXT PRIMARY KEY, session_id TEXT, model TEXT, prompt_tokens INTEGER, completion_tokens INTEGER, total_tokens INTEGER, tool_count INTEGER, tool_time_ms INTEGER, latency_ms INTEGER, created_at INTEGER)

Changes:
- src/main/db/database-implementation.ts:1
  - Create new tables and indexes in setupDatabase()
  - Add prepared statements and methods for: createSession, appendMessage, updateMessages, insertToolExecution, insertUsageSummary, getSession, listSessions, listToolExecutions, getUsageSummaryBySession
- src/main/db/database-bridge.ts:1
  - Promise-wrapped methods mirroring above
- src/main/api-route-handlers.ts:192
  - In `handleChat`:
    - Generate `sessionId` (from request or new) and `turnId` per request
    - Wire stream hooks to: record tool invocations, measure durations, and write usage summary on finish
    - Persist messages (last N turns) to `chat_sessions.messages` as JSON
  - Note: Vercel AI SDK `streamText` exposes streaming events and tool lifecycle; use these callbacks to collect metrics

Retention and pruning:
- Store only the last N messages (e.g., 50) or up to a size cap per session (e.g., 256 KB) in `chat_sessions.messages`.
- Add indexes and a pruning job for `tool_executions` and `usage_summary` (e.g., remove entries older than 90 days or keep the last M per session).
- Add DB methods to prune; invoke periodically (on app start and weekly timer) as best-effort.

Validation:
- Unit: DB roundtrips for all new methods
- Integration: chat request produces session + usage + optional tool logs


### 3.3 Session Export (IPC + CLI)

Deliverables:
- Export session to JSON via IPC and CLI

Changes:
- Main IPC handler
  - src/main/main.ts:1
    - Add `ipcMain.handle('agent:export-session', async (_evt, sessionId: string, outPath?: string) => { ... })`
    - Fetch via DatabaseBridge, shape payload per docs; write to `Downloads` or provided path
- Preload exposure
  - src/main/preload.ts:1 — expose `agentExportSession(sessionId, outPath?)` via contextBridge or reuse `ipcRenderer.invoke`
- HTTP export route (for CLI and external tooling)
  - src/main/api-server.ts:1 — register `POST /api/v1/agent/export-session`
  - src/main/api-route-handlers.ts:1 — implement handler to return `{ file }` if writing to disk or the full JSON payload if `download=false` is provided.
  - CLI should call the HTTP route (consistent with other CLI commands) and allow `--out` or `--stdout`.
- CLI command
  - cli/src/commands/sessions.ts (new) — `pasteflow export-session --id <SESSION_ID> [--out ./session.json|--stdout]`
  - Hook: cli/src/index.ts:1 — attach command

Validation:
- Unit: IPC handler validates sessionId and writes file
- API: unit/integration tests for `POST /api/v1/agent/export-session` (invalid id, default output path, download=false)
- CLI: E2E against running app (manual/local), or mocked client to assert route invocation and output handling


### 3.4 IPC Bridge Extensions (Agent)

Deliverables:
- Add agent-channel stubs that integrate with existing server or DB

Changes:
- Main process
  - src/main/main.ts:1 — add handlers for:
    - `agent:start-session` → returns `sessionId` (new or existing)
    - `agent:send-message` → optional: proxy to HTTP `/api/v1/chat` or register for future use
    - `agent:execute-tool` → call into `getAgentTools()` directly with validation; return result
    - `agent:get-history` → read `chat_sessions.messages`
- Preload
  - src/main/preload.ts:1 — expose safe wrappers for the above
- Zod schemas for IPC
  - src/main/ipc/schemas.ts:1 — add request/response zod schemas for the agent channels

Notes:
- AgentPanel currently uses HTTP useChat; these IPCs provide optional alternate control paths and are used by export and future features

Validation:
- Unit: IPC schema validation for each channel; happy path for tool execution stub


### 3.5 AgentPanel Export Control (Renderer)

Deliverables:
- Add an export control in the Agent Panel header that triggers a session export.

Changes:
- src/components/agent-panel.tsx:1
  - Add a header button "Export Session" that calls `window.electron.ipcRenderer.invoke('agent:export-session', currentSessionId)` or hits the HTTP route (if sessionId is tracked in renderer state).
  - Provide simple success feedback (e.g., a banner with the file path) and error feedback.
- Tests: add a renderer test asserting that clicking the button calls the invoke/HTTP path with a plausible sessionId and handles errors gracefully.

Validation:
- Jest test ensures invoke is called; mock IPC or fetch accordingly.


### 3.5 Code Generation Templates (Tool + Library)

Deliverables:
- Template library and `generateFromTemplate` tool that returns a file preview set

Changes:
- Template library
  - src/main/agent/templates/
    - component.ts — PascalCase export, kebab-case filename in src/components/, optional CSS + test stub
    - hook.ts — `use-name` hook in src/hooks/
    - api-route.ts — scaffold for new Express handler module under src/main/routes/ (or feature folder), including registration hint
    - test.ts — React or API test template under src/__tests__/ or src/main/__tests__/
  - src/main/agent/template-engine.ts — common helpers: casing, file path resolution, token counting integration
- Tool integration
  - src/main/agent/tools.ts:1 — add `generateFromTemplate` tool as specified (docs: 1386-1418)
  - Return shape: `{ files: Array<{ path, content }>, preview, tokenCount }`
  - Do not write to disk; writing is done via Phase 4 edit/write flow and approvals (or future Phase 5 bulk apply)

Validation:
- Unit: generation correctness (paths, names, content includes provided name), token count estimation present


### 3.6 Write/Apply Path (enable gated apply for diffs)

Deliverables:
- Enable `edit.apply` under approval and security controls

Changes:
- src/main/agent/tools.ts:1 — in `edit` tool, when `apply === true`:
  - Validate path via `AgentSecurityManager`
  - Apply unified diff using library (install `diff`/`unified-diff`) or keep current safe applier
  - Write file via a new safe helper in `src/main/file-service.ts` (add `writeTextFile` with workspace validation)
- package.json:1 — add `diff`, `unified-diff`

Validation:
- Unit: write succeeds inside workspace, fails outside; diff application correctness on simple hunks


### 3.7 Configuration and Feature Flags Wiring

Deliverables:
- Central read of flags in main; non-blocking hints in renderer

Changes:
- src/main/agent/config.ts — implement getters with precedence: prefs > env > defaults
- src/main/api-route-handlers.ts:192 — consult config/flags to set caps (MAX_TOOLS_PER_TURN, etc.)
- src/components/agent-panel.tsx:1 — optional: read and display write/terminal gates, if helpful (non-blocking)

Validation:
- Unit: config precedence works and caps enforce behavior


### 3.8 Tool System Consolidation & Search Completion

Deliverables:
- Align tools with the consolidated 5‑tool architecture from the plan and fill search gaps flagged by the audit.

Changes:
- src/main/agent/tools.ts:1
  - File tool: implement actions `read`, `write` (gated), `move`, `delete`, `info`, `list` with zod schemas; all actions consult `AgentSecurityManager`.
  - Search tool: add `files` action (filename/glob search) and `searchInContext` action; keep `code` search backed by ripgrep.
  - Gate destructive actions (`write`, `move`, `delete`) behind `ENABLE_FILE_WRITE` and approval mode (require UI approval when mode = `always`).
- Tests: cover new actions (happy and denied cases) and search variants.

Validation:
- Unit tests for each action; ensure outside-workspace paths are rejected.


### 3.9 Backpressure & Result Caps

Deliverables:
- Prevent large result sets and payloads from overloading the UI or transport.

Changes:
- Apply caps and chunking to tool results (search and diffs) and SSE messages.
  - Add truncation flags (e.g., `{ truncated: true }`, `{ clipped: true }`) to structured results.
- src/main/agent/config.ts — expose caps (`MAX_TOOLS_PER_TURN`, `MAX_RESULTS_PER_TOOL`, `MAX_SEARCH_MATCHES`, etc.).
- src/main/agent/tools.ts:1 — enforce per-tool caps and chunking where appropriate.
- src/main/api-route-handlers.ts:192 — enforce per-turn caps and return 429 on rate-limit.

Validation:
- Unit/integration tests asserting truncation flags and 429 behavior.


## 4) Dependencies & Scripts

- package.json:1 — add runtime deps
  - "diff": ^5.x
  - "unified-diff": ^4.x
- Note: Terminal deps (`xterm`, `node-pty`) remain in the Terminal track (Phase 3 follow-up). Only reference here if terminal work is pulled into Phase 4.
- No new devDependencies required for Phase 4 core features; zod already present


## 5) Testing Plan

Unit tests (Jest + ts-jest):
- Security Manager: deny outside-workspace paths, block dangerous commands
- Template engine: path casing and file placement
- Tools: `generateFromTemplate` returns correct shape; `edit.apply` (behind flag) writes and validates diffs
- DB persistence: insert/get session, tool executions, usage summary
- IPC schemas: validate `agent:*` requests
- Negative cases:
  - Diff application failures: overlapping hunks, context mismatch, and "\\ No newline at end of file" handling
  - IPC invalid inputs: bad `sessionId`, malformed tool args
  - Export: invalid id returns 404/400, path permission errors produce a structured error
  - Search caps: truncation flags present when exceeding limits

Integration tests:
- Chat flow persists a session and usage summary
  - POST /api/v1/chat with small conversation; assert DB rows exist
- Export session via HTTP route writes/returns a well-formed JSON payload with messages, tools, usage
- Tool execution path: trigger `file.read` + `search` and verify `tool_executions` rows

Renderer tests (addition to existing):
- Minimal: presence of export button in AgentPanel header and a triggered IPC/HTTP call stub


## 6) Task Breakdown & Sequencing

Order of execution (safe, incremental):
1) Configuration + feature flags
2) Security Manager module
3) Tool consolidation & search completion
4) DB schema + bridge methods (w/ retention & pruning)
5) Chat route instrumentation (session + usage + tool logs + per-session rate limiting)
6) Backpressure caps in tools and chat
7) IPC bridge extensions (agent:*)
8) Session export (HTTP route + IPC + CLI)
9) Code generation templates + tool
10) Enable `edit.apply` under approval
11) Tests and docs

Each step should compile independently; introduce flags to keep new paths disabled until verified.


## 7) Acceptance Criteria

- `generateFromTemplate` tool returns valid file previews for component/hook/api-route/test
- Chat requests create or update a `chat_sessions` row and a `usage_summary` row; `tool_executions` captured when tools run
- Export available via both IPC and `POST /api/v1/agent/export-session`; produces a JSON with session, tool executions, and usage summary or writes to disk and returns `{ file }`
- Security Manager enforces workspace boundaries and rejects dangerous commands
- IPC channels exist and validate input with zod
- All new methods covered by unit tests; integration tests pass locally via `npm test`
- AgentPanel shows an Export button that triggers export and surfaces success/failure
- Tool system supports consolidated actions (file: read/write/move/delete/info/list; search: code/files/searchInContext)
- Backpressure caps and truncation flags are enforced (no unbounded payloads)
- Session retention and pruning are in place


## 8) Risks & Mitigations

- Data migrations: SQLite schema additions must be idempotent (CREATE TABLE IF NOT EXISTS). Add indexes for performance.
- Write safety: Gate all writes behind `ENABLE_FILE_WRITE` and approval-mode controlled prompts; consider dry-run mode by default.
- Rate limiting: Start with simple in-memory per-session caps; later move to persisted counters if needed.
- SDK callbacks: Ensure stream hooks used for telemetry are robust to abort/close; always guard with try/catch.
- Cross-platform: Path validation uses existing getPathValidator; keep Windows path edge cases in mind when building relative paths.


## 9) Work Items (Checklist)

- Config surface
  - [ ] Implement src/main/agent/config.ts and defaults
  - [ ] Wire flags in chat/tools
- Security Manager
  - [ ] Implement src/main/agent/security-manager.ts
  - [ ] Integrate checks in tools + chat route
- DB + Persistence
  - [ ] Extend schema and statements in src/main/db/database-implementation.ts
  - [ ] Add bridge methods in src/main/db/database-bridge.ts
  - [ ] Add retention/pruning and indexes; expose prune methods
  - [ ] Hook handleChat for session/usage/tool logs and rate limiting
- IPC
  - [ ] Add agent channels in src/main/main.ts and schemas in src/main/ipc/schemas.ts
  - [ ] Expose preload wrappers in src/main/preload.ts
- Export
  - [ ] Implement `agent:export-session` IPC handler
  - [ ] Add HTTP route `POST /api/v1/agent/export-session`
  - [ ] Add CLI command cli/src/commands/sessions.ts and register (use HTTP route)
- AgentPanel (Renderer)
  - [ ] Add Export Session button and wire to IPC/HTTP
- Templates
  - [ ] Add template library under src/main/agent/templates and template-engine
  - [ ] Add `generateFromTemplate` tool to src/main/agent/tools.ts
- Write path
  - [ ] Add safe file write helper in file-service; enable `edit.apply` under approvals
- Tests
  - [ ] Add unit tests under src/main/__tests__ and src/__tests__ for renderer stubs
  - [ ] Add integration tests for chat persistence and export
  - [ ] Add negative tests (diff failures, IPC schema invalids, export invalid id, search truncation)


## 10) Notes on Non-Goals and Adjacent Work

- Terminal integration (node-pty + xterm.js) is tracked under Terminal work (Phase 3 audit) and will land in its own milestone unless explicitly pulled into Phase 4.
- UI enhancements (ModelSelector, TokenCounter, MiniFileList, advanced tool visualization) are not required for Phase 4 completion, but can be scheduled as Phase 3.5 or Phase 5.
- Rate limit retries and telemetry streaming at the HTTP layer are recommended but not required to consider Phase 4 complete; basic per-session rate limits via the security manager are sufficient here.
 
## 11) Interface Stubs (Code Snippets)

These TypeScript stubs clarify shapes, imports, and intended behavior without enforcing implementation specifics. They follow PasteFlow conventions (2 spaces, double quotes, semicolons) and Zod for validation.

### 11.1 Agent Config

File: `src/main/agent/config.ts`

```typescript
import type { DatabaseBridge } from "../db/database-bridge";

export interface AgentConfig {
  // Provider/model
  DEFAULT_MODEL: string; // e.g., "gpt-4o-mini" | "claude-3-haiku" | "custom"

  // Limits
  MAX_CONTEXT_TOKENS: number;   // default: 128_000
  MAX_OUTPUT_TOKENS: number;    // default: 4_096
  MAX_TOOLS_PER_TURN: number;   // default: 10
  MAX_RESULTS_PER_TOOL: number; // default: 3_000
  MAX_SEARCH_MATCHES: number;   // default: 3_000

  // Features
  ENABLE_CODE_EXECUTION: boolean;
  ENABLE_FILE_WRITE: boolean;
  APPROVAL_MODE: 'never' | 'risky' | 'always';
}

const DEFAULTS: AgentConfig = {
  DEFAULT_MODEL: process.env.PF_DEFAULT_MODEL || "gpt-4o-mini",
  MAX_CONTEXT_TOKENS: Number(process.env.PF_MAX_CONTEXT_TOKENS || 128_000),
  MAX_OUTPUT_TOKENS: Number(process.env.PF_MAX_OUTPUT_TOKENS || 4_096),
  MAX_TOOLS_PER_TURN: Number(process.env.PF_MAX_TOOLS_PER_TURN || 10),
  MAX_RESULTS_PER_TOOL: Number(process.env.PF_MAX_RESULTS_PER_TOOL || 3_000),
  MAX_SEARCH_MATCHES: Number(process.env.PF_MAX_SEARCH_MATCHES || 3_000),
  ENABLE_CODE_EXECUTION: (process.env.PF_ENABLE_CODE_EXECUTION || "false").toLowerCase() === "true",
  ENABLE_FILE_WRITE: (process.env.PF_ENABLE_FILE_WRITE || "false").toLowerCase() === "true",
  APPROVAL_MODE: (process.env.PF_AGENT_APPROVAL_MODE || 'risky'),
};

export async function getAgentConfig(opts: { db?: DatabaseBridge } = {}): Promise<AgentConfig> {
  // TODO: If a preference-based config is desired, read from opts.db.getPreference('agent.config') and merge
  // with env and DEFAULTS. For Phase 4, env + defaults is acceptable.
  return { ...DEFAULTS };
}

export async function getAgentFlags(opts: { db?: DatabaseBridge } = {}): Promise<{
  ENABLE_FILE_WRITE: boolean;
  ENABLE_CODE_EXECUTION: boolean;
  APPROVAL_MODE: 'never' | 'risky' | 'always';
}> {
  const cfg = await getAgentConfig(opts);
  return {
    ENABLE_FILE_WRITE: cfg.ENABLE_FILE_WRITE,
    ENABLE_CODE_EXECUTION: cfg.ENABLE_CODE_EXECUTION,
    APPROVAL_MODE: cfg.APPROVAL_MODE,
  };
}
```

### 11.2 Security Manager

File: `src/main/agent/security-manager.ts`

```typescript
import { getPathValidator } from "../security/path-validator";

export type RateCheck = { ok: boolean; resetInMs?: number; reason?: string };
export type AccessCheck = { ok: boolean; reason?: string };

export class AgentSecurityManager {
  private requests: Map<string, { count: number; windowStart: number }> = new Map();
  private readonly perMinute: number;
  private readonly allowedRoots: readonly string[];

  constructor(opts: { allowedRoots: readonly string[]; requestsPerMinute?: number }) {
    this.allowedRoots = opts.allowedRoots;
    this.perMinute = Math.max(1, Number(opts.requestsPerMinute || 100));
  }

  validateFileAccess(path: string): AccessCheck {
    const validator = getPathValidator(this.allowedRoots);
    const res = validator.validatePath(path);
    return res.valid ? { ok: true } : { ok: false, reason: res.reason };
  }

  validateCommand(command: string): AccessCheck {
    const danger = ["rm -rf", "mkfs", ":(){:|:&};:", "del /f", "format ", "shutdown "];
    const lower = String(command || "").toLowerCase();
    const blocked = danger.some(d => lower.includes(d));
    return blocked ? { ok: false, reason: "dangerous-command" } : { ok: true };
  }

  checkRateLimit(sessionId: string): RateCheck {
    const now = Date.now();
    const windowMs = 60_000;
    const entry = this.requests.get(sessionId) || { count: 0, windowStart: now };
    if (now - entry.windowStart >= windowMs) {
      entry.count = 0;
      entry.windowStart = now;
    }
    if (entry.count + 1 > this.perMinute) {
      const resetInMs = Math.max(0, entry.windowStart + windowMs - now);
      return { ok: false, resetInMs, reason: "rate-limit" };
    }
    entry.count += 1;
    this.requests.set(sessionId, entry);
    return { ok: true };
  }
}
```

### 11.3 Tool Action Schemas (Zod)

File: `src/main/agent/tool-schemas.ts`

```typescript
import { z } from "zod";

// File tool
export const FileReadParams = z.object({
  action: z.literal("read"),
  path: z.string(),
  lines: z.object({ start: z.number().int().min(1), end: z.number().int().min(1) })
    .refine(v => v.end >= v.start, { message: "end must be >= start" })
    .optional(),
});

export const FileWriteParams = z.object({
  action: z.literal("write"),
  path: z.string(),
  content: z.string(),
});

export const FileMoveParams = z.object({
  action: z.literal("move"),
  path: z.string(),
  newPath: z.string(),
});

export const FileDeleteParams = z.object({
  action: z.literal("delete"),
  path: z.string(),
});

export const FileInfoParams = z.object({
  action: z.literal("info"),
  path: z.string(),
});

export const FileListParams = z.object({
  action: z.literal("list"),
  directory: z.string().optional(),
  glob: z.string().optional(),
  limit: z.number().int().min(1).max(5000).optional(),
  paths: z.array(z.string()).optional(),
});

export const FileToolParams = z.discriminatedUnion("action", [
  FileReadParams,
  FileWriteParams,
  FileMoveParams,
  FileDeleteParams,
  FileInfoParams,
  FileListParams,
]);

// Search tool
export const SearchCodeParams = z.object({
  action: z.literal("code"),
  query: z.string().min(1).max(256),
  directory: z.string().optional(),
  regex: z.boolean().optional(),
  maxResults: z.number().int().min(1).max(5000).optional(),
});

export const SearchFilesParams = z.object({
  action: z.literal("files"),
  query: z.string().min(1).max(256), // substring or glob
  directory: z.string().optional(),
  limit: z.number().int().min(1).max(5000).optional(),
});

export const SearchInContextParams = z.object({
  action: z.literal("searchInContext"),
  query: z.string().min(1).max(256),
  regex: z.boolean().optional(),
});

export const SearchToolParams = z.discriminatedUnion("action", [
  SearchCodeParams,
  SearchFilesParams,
  SearchInContextParams,
]);

// Generation tool
export const GenerateFromTemplateParams = z.object({
  template: z.enum(["component", "hook", "api-route", "test"]),
  name: z.string().min(1).max(200),
  options: z.record(z.any()).optional(),
});

// Edit tool
export const EditParams = z.object({
  path: z.string(),
  diff: z.string(),
  apply: z.boolean().default(false),
});

// Terminal tool (stub)
export const TerminalParams = z.object({
  command: z.string().min(1),
  cwd: z.string().optional(),
  env: z.record(z.string()).optional(),
});
```

Example integration outline (using Vercel AI SDK tools):

```typescript
import { tool } from "ai";
import { z } from "zod";
import { FileToolParams, SearchToolParams, GenerateFromTemplateParams, EditParams, TerminalParams } from "./tool-schemas";
import { AgentSecurityManager } from "./security-manager";

export function getAgentTools(ctx: { security: AgentSecurityManager }) {
  const file = tool({
    description: "File operations: read/write/move/delete/info/list",
    parameters: FileToolParams,
    execute: async (params) => {
      // TODO: switch (params.action) and consult ctx.security before destructive ops
      return { notImplemented: true };
    },
  });

  const search = tool({
    description: "Search code, files, or in-context",
    parameters: SearchToolParams,
    execute: async (params) => {
      // TODO: switch (params.action) to call ripgrep, filename search, or in-memory search
      return { notImplemented: true };
    },
  });

  const generateFromTemplate = tool({
    description: "Generate code using templates (preview only)",
    parameters: GenerateFromTemplateParams,
    execute: async (_params) => {
      // TODO: call template-engine and return files + preview
      return { files: [], preview: "", tokenCount: 0 };
    },
  });

  const edit = tool({
    description: "Preview or apply unified diff",
    parameters: EditParams,
    execute: async (_params) => {
      // TODO: reuse existing preview logic; gate apply behind approvals
      return { type: "error", message: "Apply requires approval" };
    },
  });

  const terminal = tool({
    description: "Terminal execution (gated)",
    parameters: TerminalParams,
    execute: async (_params) => ({ notImplemented: true }),
  });

  return { file, search, generateFromTemplate, edit, terminal } as const;
}
```

### 11.4 Result Shapes & Minimal Examples

Provide consistent, compact result shapes to keep UI and logs predictable.

```typescript
// File tool results
export interface FileReadResult {
  path: string;
  content: string;
  tokenCount: number;
}

export interface FileWriteResult {
  path: string;
  bytes: number;
  updatedAt: number; // epoch ms
}

export interface FileMoveResult { from: string; to: string }
export interface FileDeleteResult { path: string; deleted: boolean }
export interface FileInfoResult {
  path: string;
  isDirectory: boolean;
  size?: number;
  mtimeMs?: number;
}
export interface FileListResult {
  entries: Array<{ path: string; isFile: boolean; size?: number }>;
  truncated?: boolean;
}

// Minimal example payloads:
const readExample: FileReadResult = {
  path: "/abs/src/app.ts",
  content: "export function x() {}\n",
  tokenCount: 6,
};

const listExample: FileListResult = {
  entries: [
    { path: "/abs/src/app.ts", isFile: true, size: 1234 },
    { path: "/abs/src/components", isFile: false },
  ],
  truncated: false,
};

// Search tool results
export interface SearchCodeResult {
  totalMatches: number;
  truncated?: boolean;
  files: Array<{
    path: string;
    matches: Array<{ line: number; text: string; ranges: Array<{ start: number; end: number }> }>;
  }>;
}

export interface SearchFilesResult {
  files: Array<{ path: string; score?: number }>;
  truncated?: boolean;
}

export interface SearchInContextResult {
  files: Array<{
    path: string;
    matches: Array<{ line: number; text: string; ranges: Array<{ start: number; end: number }> }>;
  }>;
}

// Minimal example payloads:
const searchCodeExample: SearchCodeResult = {
  totalMatches: 3,
  files: [
    {
      path: "/abs/src/app.ts",
      matches: [
        { line: 10, text: "const foo = bar;", ranges: [{ start: 6, end: 9 }] },
      ],
    },
  ],
};

const searchFilesExample: SearchFilesResult = {
  files: [ { path: "/abs/src/app.ts", score: 0.92 } ],
  truncated: false,
};
```

### 11.5 Template Engine Outline (Preview‑Only)

File: `src/main/agent/template-engine.ts`

```typescript
import { getMainTokenService } from "../../services/token-service-main";

export type TemplateKind = "component" | "hook" | "api-route" | "test";

export interface GeneratedFile { path: string; content: string }
export interface TemplateResult {
  files: GeneratedFile[];
  preview: string;
  tokenCount: number;
}

function toKebabCase(name: string): string {
  return name
    .replace(/([a-z])([A-Z])/g, "$1-$2")
    .replace(/\s+/g, "-")
    .toLowerCase();
}

function toPascalCase(name: string): string {
  return name
    .replace(/(^\w|[\s-_]+\w)/g, (m) => m.replace(/[\s-_]+/g, "").toUpperCase());
}

export async function generateFromTemplate(
  kind: TemplateKind,
  name: string,
  options: Record<string, unknown> = {}
): Promise<TemplateResult> {
  const kebab = toKebabCase(name);
  const pascal = toPascalCase(name);
  const files: GeneratedFile[] = [];

  switch (kind) {
    case "component": {
      const path = `src/components/${kebab}.tsx`;
      const content = `export default function ${pascal}() {\n  return (<div>${pascal}</div>);\n}\n`;
      files.push({ path, content });
      // Optional co-located test
      files.push({ path: `src/__tests__/${kebab}.test.tsx`, content: `import ${pascal} from '../components/${kebab}';\n\n test('${pascal} renders', () => {/* ... */});\n` });
      break;
    }
    case "hook": {
      const path = `src/hooks/use-${kebab}.ts`;
      const content = `export function use${pascal}() {\n  // TODO: implement\n  return {};\n}\n`;
      files.push({ path, content });
      files.push({ path: `src/__tests__/use-${kebab}.test.ts`, content: `import { use${pascal} } from '../hooks/use-${kebab}';\n\n test('use${pascal} basic', () => {/* ... */});\n` });
      break;
    }
    case "api-route": {
      const path = `src/main/routes/${kebab}.ts`;
      const content = `import type { Request, Response } from 'express';\nexport function ${kebab}Route(req: Request, res: Response) {\n  return res.json({ ok: true });\n}\n`;
      files.push({ path, content });
      break;
    }
    case "test": {
      const path = `src/__tests__/${kebab}.test.ts`;
      const content = `describe('${pascal}', () => {\n  it('works', () => { expect(1+1).toBe(2); });\n});\n`;
      files.push({ path, content });
      break;
    }
  }

  const preview = files.map(f => `File: ${f.path}\n\`\`\`\n${f.content}\n\`\`\``).join("\n\n");
  const tokenService = getMainTokenService();
  const { count } = await tokenService.countTokens(preview);
  return { files, preview, tokenCount: count };
}
```

---

Appendix — Key Source References
- Audit: docs/vercel-ai-sdk-agent-integration-audit.md:255, 371, 384
- Plan: docs/vercel-ai-sdk-agent-integration.md:1386-1418, 1916-1925, 1928-1948, 1950-1978, 2498-2516, 2519-2553
- Chat route: src/main/api-route-handlers.ts:192
- Tools: src/main/agent/tools.ts:1
- DB implementation: src/main/db/database-implementation.ts:1
- DB bridge: src/main/db/database-bridge.ts:1
- Preload: src/main/preload.ts:1
- Main: src/main/main.ts:1
- CLI entry: cli/src/index.ts:1
