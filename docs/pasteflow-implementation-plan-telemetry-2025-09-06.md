# Implementation Plan — SDK Finish Telemetry (Usage Tokens + Latency) — 2025-09-06

## Scope Summary
- Persist token usage from `streamText` finish event and capture server-side latency for each chat request.
- Replace placeholder `usage_summary` inserts with accurate data collected via `onFinish`.
- Optional DB schema enhancement to store `latency_ms` (nullable) for longitudinal analysis.

## Current Code Analysis
- Chat handler: `src/main/api-route-handlers.ts:262` (`APIRouteHandlers.handleChat`)
  - Creates stream: `streamText(...)` at `src/main/api-route-handlers.ts:389` (main path) and `:469` (fallback without tools).
  - Pipes response: `pipeUIMessageStreamToResponse` at `:403` and `:480`.
  - Writes placeholder usage row with nulls after piping at `:418` and `:491`.
  - Has helper error classifiers and session persistence.
- DB schema and API:
  - Table: `usage_summary(id, session_id, input_tokens, output_tokens, total_tokens, created_at)` in `src/main/db/database-implementation.ts:247-268`.
  - Insert prepared statement + method: `stmtInsertUsageSummary` and `insertUsageSummary(sessionId, input, output, total)` at `src/main/db/database-implementation.ts:410` and `:627`.
  - Bridge method: `DatabaseBridge.insertUsageSummary(...)` at `src/main/db/database-bridge.ts:275`.
- Renderer side finish handling (for context): `src/components/agent-panel.tsx:321` uses `onFinish` for UI snapshot; server currently does not consume SDK finish usage.

## Design Overview
- Record `const start = Date.now()` just before calling `streamText`.
- Provide `onFinish` callback to `streamText` to receive `usage` and compute latency.
- Persist a single row to `usage_summary` per successful streamed request in `onFinish`.
- Remove post-pipe placeholder inserts that currently write nulls.
- Optional: add `latency_ms` INTEGER column to `usage_summary` (nullable). If not added, log latency in development builds.

## Step-by-Step Implementation
1) Add optional latency column (Option A; recommended)
- File: `src/main/db/database-implementation.ts`
  - During `initializeDatabase()` (schema creation block), add a conditional migration:
    - Query column info: `PRAGMA table_info('usage_summary')` and check for a `latency_ms` column.
    - If missing, run: `ALTER TABLE usage_summary ADD COLUMN latency_ms INTEGER;`
  - Add prepared statement for 5-field insert:
    - Signature: `private stmtInsertUsageSummaryWithLatency(): BetterSqlite3.Statement<[string, number|null, number|null, number|null, number|null]>`.
    - SQL: `INSERT INTO usage_summary (session_id, input_tokens, output_tokens, total_tokens, latency_ms) VALUES (?, ?, ?, ?, ?)`.
  - Add public method:
    - `async insertUsageSummaryWithLatency(sessionId: string, input: number|null, output: number|null, total: number|null, latencyMs: number|null): Promise<void>`.
- File: `src/main/db/database-bridge.ts`
  - Add wrapper method:
    - `async insertUsageSummaryWithLatency(sessionId: string, input: number|null, output: number|null, total: number|null, latencyMs: number|null)`
    - Pass-through to underlying database implementation if available; fallback to `insertUsageSummary` when not.

2) Wire `onFinish` usage capture in chat handler
- File: `src/main/api-route-handlers.ts`
  - In `APIRouteHandlers.handleChat` main path:
    - Just before `const result = streamText({ ... })`, add `const start = Date.now();`.
    - Extend `streamText` call options with:
      - `onFinish: async (info: any) => { try { const u = info?.usage || {}; const input = typeof u.inputTokens === 'number' ? u.inputTokens : null; const output = typeof u.outputTokens === 'number' ? u.outputTokens : null; const total = typeof u.totalTokens === 'number' ? u.totalTokens : (input != null && output != null ? input + output : null); const latency = Date.now() - start; if ((this.db as any).insertUsageSummaryWithLatency) { await (this.db as any).insertUsageSummaryWithLatency(sessionId, input, output, total, latency); } else { await this.db.insertUsageSummary(sessionId, input, output, total); } } catch {/* ignore */} }`
      - Keep `onAbort` as-is.
    - Remove the placeholder usage insert currently at `:418`.
  - In the toolless fallback path:
    - Mirror the above changes: add `start`, add `onFinish`, remove the placeholder at `:491`.
  - Do not attempt to set response headers for latency; headers may be sent during streaming.

3) Defensive handling for providers without usage
- Persist nulls when usage fields are unavailable.
- If provider supplies only total tokens, map accordingly.
- Ensure this code path does not throw; all writes are best-effort with try/catch.

4) Optional: Development log line
- Inside `onFinish`, when `NODE_ENV === 'development'`, log `{ provider, model, input, output, total, latency }` if resolvable from config.

5) Remove placeholder null inserts
- Delete or guard the existing `insertUsageSummary(sessionId, null, null, null)` after the piping calls in both paths.

## Function/Method Signatures
- New (DB impl):
  - `private stmtInsertUsageSummaryWithLatency(): BetterSqlite3.Statement<[string, number|null, number|null, number|null, number|null]>`
  - `async insertUsageSummaryWithLatency(sessionId: string, input: number|null, output: number|null, total: number|null, latencyMs: number|null): Promise<void>`
- New (DB bridge):
  - `async insertUsageSummaryWithLatency(sessionId: string, input: number|null, output: number|null, total: number|null, latencyMs: number|null): Promise<void>`
- Modified (chat handler): add `onFinish` in `streamText` options (no signature change to public APIs).

## Database Schema Changes
- SQL migration (runtime, idempotent):
  - `PRAGMA table_info('usage_summary');`
  - If `latency_ms` absent: `ALTER TABLE usage_summary ADD COLUMN latency_ms INTEGER;`
- No table recreation; avoids data loss and is backward compatible.

## API Endpoint Changes
- Endpoint: Chat stream (`handleChat` in `src/main/api-route-handlers.ts`)
- Request: unchanged.
- Response: unchanged (no new headers; latency not exposed via response headers).

## Configuration Changes
- None required. Optional: add a pref/env to toggle telemetry capture if desired (out of scope for now).

## Dependencies
- No new NPM packages.
- Uses existing `ai` SDK hooks.

## Error Handling & Edge Cases
- `onFinish` may not fire for aborted/failed requests; do not rely on it for error counting.
- Usage may be partially present or absent; persist nulls safely.
- DB writes are best-effort; wrap in try/catch to avoid impacting the stream.

## Backward Compatibility & Migration Strategy
- Existing `insertUsageSummary` remains; bridge method with latency falls back to 4-field insert.
- Runtime migration adds a nullable column; existing rows remain valid.
- No changes to API surface.

## Risk Assessment & Regression Prevention
- Affected areas: chat streaming, DB writes to `usage_summary`.
- Low risk: changes are additive and guarded.
- Update tests:
  - `src/main/__tests__/api-chat-errors.test.ts`: ensure no regressions in error handling.
  - New: `src/main/__tests__/api-chat-usage-telemetry.test.ts` to simulate a finish event and assert DB insert called with expected values (mock DB bridge, mock `streamText` to trigger `onFinish`).
- Manual verification: inspect DB rows after a few chats; confirm latency looks reasonable.

## Implementation Order
1. DB migration + new statements/methods (impl + bridge).
2. Add `onFinish` usage capture to chat handler (main + fallback) and remove placeholders.
3. Add tests and run suite.

## Shared Components / Dependencies with Other Improvements
- Shared with 429 retry: `handleChat` flow; ensure retry implementation does not interfere with `onFinish` (only retry before stream creation).
- Independent of context tool changes.
