# Agent Panel Chat — API Key Error Handling Plan

Date: 2025-09-03
Scope: Analysis + implementation plan only (no code changes).


## Summary
- Problem: When a user sends a chat message without a configured OpenAI API key, the main process throws `LoadAPIKeyError` from the Vercel AI SDK, but the Agent Panel UI shows no feedback.
- Goal: Detect provider API key misconfiguration, return a stable server error shape/status, and display a terse, actionable banner in the Agent Panel without breaking the chat.


## Investigation Findings

- Renderer (Agent Panel)
  - File: `src/components/agent-panel.tsx`
    - Uses `@ai-sdk/react` `useChat` to POST to the local API: `${apiBase}/api/v1/chat`.
    - Custom `fetch` ensures absolute API URL and attaches `Authorization` from `window.__PF_API_INFO` (injected by Electron main).
    - Builds a structured `context` envelope (initial + dynamic) via `prepareSendMessagesRequest`.
    - Loading states: header banner shows “Streaming…” or “Ready”.
    - Error handling: `onError` only captures HTTP 429 and sets `errorStatus = 429` to show a red banner. No handling for provider configuration errors.
    - On finish: clears attachments and resets `errorStatus` to null.
  - Related renderer components: `agent-chat-input.tsx`, `agent-attachment-list.tsx`, `agent-tool-calls.tsx`.
  - Existing test: `src/__tests__/agent-error-banner-429.test.tsx` verifies the 429 banner.

- Main process (API)
  - Chat route: `src/main/api-route-handlers.ts` `handleChat(req, res)`
    - Converts UI messages via `convertToModelMessages` and composes a system prompt via `buildSystemPrompt`.
    - Calls `streamText({ model: openai('gpt-4o-mini'), ... })` and streams to the response via `pipeUIMessageStreamToResponse`.
    - Errors: generic `try/catch` returns `500` with `{ error: { code: 'SERVER_ERROR', message } }` using `toApiError`.
    - No special handling for `LoadAPIKeyError` (aka `AI_LoadAPIKeyError`) when `OPENAI_API_KEY` is missing.
  - Error schema: `src/main/error-normalizer.ts` does not currently include a code for provider configuration (e.g., API key missing).
  - Auth: `src/main/api-server.ts` applies `Authorization` middleware and returns `401` for unauthenticated requests (should not be used to signal provider config problems).

- Error observed
  - Terminal shows: `LoadAPIKeyError [AI_LoadAPIKeyError]: OpenAI API key is missing. Pass it using the 'apiKey' parameter or the OPENAI_API_KEY environment variable.`
  - Renderer receives a generic failure; no specific user-facing message.


## Design Overview

- Classify provider configuration errors in `handleChat` and return a dedicated status + error code.
- In the Agent Panel, map that status to a concise banner with actionable guidance. Keep the chat interactive.


## Server Changes (Main Process)

- Files to modify
  - `src/main/error-normalizer.ts`
    - Add a new error code: `AI_PROVIDER_CONFIG` (or alternately `OPENAI_API_KEY_MISSING`).
  - `src/main/api-route-handlers.ts`
    - Enhance the `catch` in `handleChat` to detect API key load errors and return a 503 Service Unavailable with a specific error code and minimal details.

- Error classification logic
  - Detection heuristics (robust and provider-agnostic):
    - `error?.name === 'AI_LoadAPIKeyError'` or includes `LoadAPIKeyError`.
    - `String(error?.message).toLowerCase()` contains `api key is missing`.
  - Response contract for missing key:
    - HTTP status: `503` (Service Unavailable). Rationale: server cannot fulfill the request due to upstream/provider configuration; avoids clashing with our `401` auth.
    - Body: `toApiError('AI_PROVIDER_CONFIG', 'OpenAI API key missing', { provider: 'openai', reason: 'api-key-missing' })`.
  - Fallback: for all other errors, keep current `500 SERVER_ERROR` behavior.

- Logging
  - Log a single-line warning for the classified case: `AI provider config error: missing OPENAI_API_KEY` (no secrets).


## Renderer Changes (Agent Panel UI)

- Files to modify
  - `src/components/agent-panel.tsx`
    - Expand `onError` to detect provider-config status and set a UI error flag.
    - Display a dismissible error banner with terse, actionable copy.

- Error handling pattern
  - Current: `const [errorStatus, setErrorStatus] = useState<number | null>(null);`
  - Proposed minimal extension: reuse this for top-level mapping and add a tiny mapper function.
    - Map `429` → Rate limit banner (existing behavior).
    - Map `503` → Provider config banner (new behavior).
    - Optional future: introduce a structured `error` state if we need multiple simultaneous banners; not needed now.

- UI banner content and tone
  - Message: “OpenAI API key missing. Set OPENAI_API_KEY and restart.”
  - Secondary line (optional, only if space permits): “See docs: docs/vercel-ai-sdk-agent-integration.md.”
  - Style: reuse existing 429 banner styling (`role="alert"`, red background, Dismiss button). Keep wording terse.

- Reset behavior
  - Clear the banner on Dismiss, and also in `onFinish()` and on new send.


## Error Message Copy

- Primary: “OpenAI API key missing. Set OPENAI_API_KEY and restart.”
- If we add a link later, reference: `docs/vercel-ai-sdk-agent-integration.md` and `docs/phase-1-implementation-guide.md` for setup.


## Data Flow After Changes

1) User clicks Send → `useChat` posts to `/api/v1/chat` with Authorization.
2) Main route attempts `streamText(...)`.
   - If `OPENAI_API_KEY` is missing, `LoadAPIKeyError` is thrown.
   - `handleChat` classifies it and returns `503` with `{ error: { code: 'AI_PROVIDER_CONFIG', ... } }`.
3) `useChat` triggers `onError` with `status: 503`.
4) Agent Panel maps `503` → provider-config banner; chat remains usable.


## Testing Plan

- Renderer tests (Jest + jsdom)
  - New: `src/__tests__/agent-error-banner-provider-config.test.tsx`
    - Render AgentPanel, type a message, click Send.
    - Use `__aiSdkMock.simulateError(503)`.
    - Expect a banner with text containing “API key missing” and a Dismiss button.
    - Click Dismiss; assert the banner is removed.
  - Regression: keep `agent-error-banner-429.test.tsx` passing.

- Main process tests (Jest + supertest)
  - New: `src/main/__tests__/api-chat-errors.test.ts`
    - Mock `streamText` (from `ai`) or `openai` to throw an error with `name = 'AI_LoadAPIKeyError'` and the canonical message.
    - POST `/api/v1/chat` with Authorization.
    - Assert HTTP 503 and body `error.code === 'AI_PROVIDER_CONFIG'` (or `OPENAI_API_KEY_MISSING`).
    - Ensure other errors still return 500 `SERVER_ERROR`.

- Manual verification
  - Start the app without `OPENAI_API_KEY` in the environment.
  - Open Agent Panel; send a simple message.
  - Observe banner showing the missing key guidance; terminal shows one concise warning.
  - Set `OPENAI_API_KEY` and restart; banner no longer appears and chat streams normally.


## Implementation Steps (Concrete)

1) Server error code
- Edit `src/main/error-normalizer.ts`
  - Add `'AI_PROVIDER_CONFIG'` to `ApiErrorCode` union.

2) Classify error in chat route
- Edit `src/main/api-route-handlers.ts` `handleChat`
  - In `catch (error) { ... }`:
    - If `isProviderConfigError(error)` → `return res.status(503).json(toApiError('AI_PROVIDER_CONFIG', 'OpenAI API key missing', { provider: 'openai', reason: 'api-key-missing' }));`
    - Else → existing `500` path.
  - Add local helper `isProviderConfigError(e: unknown)` using the heuristics above.

3) Renderer banner mapping
- Edit `src/components/agent-panel.tsx`
  - Update `onError`: read `err?.status`.
    - If `429` → `setErrorStatus(429)`.
    - If `503` → `setErrorStatus(503)`.
  - In the JSX above messages, add conditional render for `errorStatus === 503` using the same banner style as the 429 case with the copy above.
  - Ensure `onFinish` and Dismiss clear the banner (`setErrorStatus(null)`).

4) Tests
- Add `src/__tests__/agent-error-banner-provider-config.test.tsx` mirroring the 429 test but using `503` and matching the banner copy.
- Add `src/main/__tests__/api-chat-errors.test.ts` to verify 503 classification.


## Risks & Considerations

- Avoid `401` for provider config to prevent confusion with app auth failures; choose `503`.
- Vercel AI SDK error names may change; keep heuristics message-based as a fallback.
- If we add support for multiple providers, extend the classifier to include provider name in `details` and update banner copy generically: “LLM provider not configured. Set API key and restart.”
- Keep messages concise to avoid cluttering the chat UI; do not block input.


## Follow-ups (Optional)

- Add a Settings → Integrations panel to manage provider API keys in-app (persisted in encrypted preferences) instead of requiring environment variables.
- Surface an inline “Configure” link/button in the banner if/when a settings UI exists.
- Telemetry: count occurrences of provider-config errors for diagnostics (without collecting secrets).


## References
- Renderer: `src/components/agent-panel.tsx`
- API route: `src/main/api-route-handlers.ts`
- Error helpers: `src/main/error-normalizer.ts`
- Tests: `src/__tests__/agent-error-banner-429.test.tsx`
- Docs: `docs/vercel-ai-sdk-agent-integration.md`, `docs/phase-1-implementation-guide.md`

