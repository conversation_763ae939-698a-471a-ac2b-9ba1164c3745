# Complete Git Changes - All Staged and Unstaged Diffs

Generated on: Sat  6 Sep 2025 00:58:33 PDT
Branch: feat/finish-telemetry

## Complete Staged Changes

```diff
diff --git a/README.md b/README.md
index e42d198..94f1ce5 100644
--- a/README.md
+++ b/README.md
@@ -40,6 +40,15 @@ Build precise, token-efficient context from any codebase. Select exact files and
 - **Dark Mode**: Light and dark themes for comfortable viewing
 - **Agent Model Switcher (WIP)**: Change AI provider/model at runtime from the Agent Panel; configure API keys in a Model Settings modal. Keys are stored locally, encrypted.
 
+### Telemetry & Costs
+- **Per‑turn telemetry (persisted)**: The app records input/output/total token usage and server‑side latency for each assistant turn.
+- **Cost tracking (server‑side)**: A minimal pricing table computes `cost_usd` for common models; costs are persisted with usage. Unknown models fall back to an approximate UI‑only estimate.
+- **UI displays**:
+  - Header chip: shows session totals — `total tokens (in: X, out: Y)` and a cost figure when available (no latency).
+  - Message rows: user messages display their token count; assistant messages display output tokens and latency. Tooltips show a full breakdown (input/output/total, latency, and cost when known).
+  - Model Settings → Session Stats: shows total input/output/overall tokens, average latency, and session cost.
+  - Exported sessions include the recorded usage rows.
+
 ## Installation
 
 Download the latest release from the Releases page.
@@ -86,6 +95,10 @@ npm run test:watch
 - PF_AGENT_MAX_SESSION_MESSAGES: persist last N chat messages per session (default: 50)
 - PF_AGENT_TELEMETRY_RETENTION_DAYS: days to retain tool/usage telemetry (default: 90)
 
+Telemetry & cost notes
+- Costs are computed server‑side using a small built‑in pricing table (per 1M tokens) in `src/main/agent/pricing.ts`. The table covers common default models and can be expanded. When a model is not in the table, the UI may show an approximate cost based on a conservative rate.
+- Some providers do not return usage tokens for every turn; in those cases the UI labels values as `(approx)` and still records latency.
+
 Notes
 - Preferences override env. Relevant preference keys: `agent.provider`, `agent.defaultModel`, `agent.temperature`, `agent.maxOutputTokens`.
 
@@ -294,6 +307,13 @@ Notes
 - `prefs set`: refreshes persisted settings across the UI.
 - `workspaces create|rename|delete`: refreshes the UI’s workspace list.
 
+### Telemetry API (IPC)
+- `agent:usage:list` → returns persisted usage rows for a session: `[{ input_tokens, output_tokens, total_tokens, latency_ms, cost_usd, created_at }]`.
+- `agent:usage:append` → internal best‑effort fallback used by the renderer to append a row when the provider doesn’t return usage (includes a locally measured latency).
+
+Notes
+- Costs in the UI prefer persisted `cost_usd` and fall back to approximations only when pricing or usage is unavailable.
+
 API: Selection token breakdown
 - Endpoint: `GET /api/v1/selection/tokens`
 - Example:
@@ -381,12 +401,68 @@ Implementation
 - API (local):
   - `GET /api/v1/models?provider=openai|anthropic|openrouter` → `{ provider, models: [{ id, label, ...}] }` (static catalog; best-effort).
   - `POST /api/v1/models/validate` → `{ ok: true } | { ok: false, error }` using a tiny generation to verify credentials/model.
+  - Telemetry is captured by the chat route and exposed via IPC; session export includes usage rows.
 
 ## Workspaces and the Database
 
 - SQLite-backed persistence for workspaces (state, prompts, instructions).
 - See src/main/db/README.md for details.
 
+## Appendix: Cost Calculation
+
+PasteFlow computes and persists turn costs on the server. The renderer only displays what is stored, and shows an approximate hint when server pricing/usage is unavailable.
+
+How it works
+- Pricing lives in `src/main/agent/pricing.ts` as a small TypeScript table keyed by `provider:modelId` (all lowercase). Rates are expressed per 1,000,000 tokens (per‑million) to match vendor docs.
+- On each assistant `onFinish`, the server computes `cost_usd` from actual usage and stores it in `usage_summary` alongside token counts and latency.
+- The UI reads rows via IPC (`agent:usage:list`) and shows:
+  - Header chip: session totals — total tokens with breakdown (in/out) and a cost figure.
+  - Assistant messages: output tokens + latency; tooltip includes input/output/total and cost.
+  - User messages: input tokens only (approximate, UI‑side) with a tooltip.
+
+Pricing table format (per‑million)
+```ts
+export type Pricing = {
+  inPerMTok: number;
+  outPerMTok: number;
+  cacheWritePerMTok?: number;
+  cacheReadPerMTok?: number;
+  thinkPerMTok?: number;
+  subscriptionFree?: boolean; // set true when usage is covered by a subscription
+};
+```
+
+Computation
+- Base: `inCost = inPerMTok * (uncachedInput / 1e6)`, `outCost = outPerMTok * (output / 1e6)`.
+- Cache (when available): add `cacheWritePerMTok * (cacheWrites / 1e6)` and `cacheReadPerMTok * (cacheReads / 1e6)`.
+- OpenAI‑style rule: if cache read/write counts exist, subtract them from input to avoid double‑counting (uncached input only).
+- Thinking tokens (when available): `thinkPerMTok * (thinking / 1e6)`.
+- If `subscriptionFree` is set, cost is forced to `0`.
+
+Example
+```
+Model: openai:gpt-4o-mini (in=$5/M, out=$15/M)
+Usage: input=2,000, output=1,000 tokens, no cache
+Cost: (5 * 2000/1e6) + (15 * 1000/1e6) = 0.010 + 0.015 = $0.0250
+```
+
+Adding or adjusting models
+1) Edit `src/main/agent/pricing.ts` and add/update an entry:
+```ts
+PRICING["openai:gpt-4o-mini"] = { inPerMTok: 5, outPerMTok: 15 };
+```
+2) Ensure the key matches `provider:modelId` as used by PasteFlow’s model resolver (e.g., `openrouter:openai/gpt-4o-mini`).
+3) Restart the app to apply.
+
+Notes & limitations
+- Persisted costs are computed server‑side only. The UI may show an approximate cost when pricing or usage is missing; those estimates are labeled `(approx)`.
+- Some entries ship as conservative placeholders (e.g., early `gpt-5` values). Update them to your contracts as needed.
+- Currency is USD; tax and discounts are not modeled. Values are shown as `$X.XXXX` for readability.
+
+Troubleshooting cost display
+- If the header chip shows `0 (approx)`, the provider likely didn’t return token usage yet; the UI is using a text‑length estimate and will switch to persisted values as they arrive in subsequent turns.
+- If costs do not appear for a model, add it to the pricing table; the UI otherwise falls back to an approximate hint.
+
 ## Contributing
 
 - Use `npm run dev:electron` for local development.
diff --git a/src/components/agent-panel.css b/src/components/agent-panel.css
index 33bba01..a316c48 100644
--- a/src/components/agent-panel.css
+++ b/src/components/agent-panel.css
@@ -200,3 +200,78 @@
   padding: 6px 12px;
   z-index: 1;
 }
+
+/* Usage chip for last response (retro, unobtrusive) */
+.agent-usage-chip {
+  display: inline-flex;
+  align-items: center;
+  gap: 6px;
+  height: 22px;
+  padding: 0 8px;
+  border-radius: 6px;
+  border: 1px solid var(--border-color);
+  background: linear-gradient(180deg, rgba(255,255,255,0.03), rgba(0,0,0,0.06));
+  color: var(--text-secondary);
+  font-size: 11px;
+  box-shadow: inset 0 1px 0 rgba(255,255,255,0.05);
+}
+
+.agent-usage-chip .dot {
+  width: 6px;
+  height: 6px;
+  border-radius: 50%;
+  background: var(--text-secondary);
+  opacity: 0.7;
+}
+
+.message-usage-row {
+  display: flex;
+  align-items: center;
+  gap: 6px;
+  margin-top: 4px;
+  color: var(--text-secondary);
+  font-size: 11px;
+}
+
+.message-usage-row .info-icon {
+  display: inline-flex;
+  align-items: center;
+  justify-content: center;
+  width: 16px;
+  height: 16px;
+  border-radius: 3px;
+  border: 1px solid var(--border-color);
+  background: var(--background-secondary);
+  color: var(--text-secondary);
+  position: relative;
+  cursor: default;
+}
+
+.message-usage-row .info-icon:hover {
+  background: var(--hover-color);
+  color: var(--text-primary);
+}
+
+/* Lightweight tooltip (retro style) */
+.message-usage-row .info-icon .tooltip-box {
+  display: none;
+  position: absolute;
+  left: 20px;
+  top: 50%;
+  transform: translateY(-50%);
+  white-space: pre-line;
+  padding: 6px 8px;
+  border-radius: 6px;
+  border: 1px solid var(--border-color);
+  background: var(--background-primary);
+  color: var(--text-primary);
+  box-shadow: 0 2px 8px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.04);
+  font-size: 11px;
+  z-index: 20;
+  min-width: 160px;
+  max-width: 280px;
+}
+
+.message-usage-row .info-icon:hover .tooltip-box {
+  display: block;
+}
diff --git a/src/components/agent-panel.tsx b/src/components/agent-panel.tsx
index 4c4cb52..8c5e360 100644
--- a/src/components/agent-panel.tsx
+++ b/src/components/agent-panel.tsx
@@ -6,7 +6,8 @@ import AgentAttachmentList from "./agent-attachment-list";
 import AgentToolCalls from "./agent-tool-calls";
 import IntegrationsModal from "./integrations-modal";
 import ModelSelector from "./model-selector";
-import { ArrowUp, List as ListIcon, Plus as PlusIcon } from "lucide-react";
+import { ArrowUp, List as ListIcon, Plus as PlusIcon, Info as InfoIcon } from "lucide-react";
+import { TOKEN_COUNTING } from "@constants";
 import ModelSettingsModal from "./model-settings-modal";
 import { Settings as SettingsIcon } from "lucide-react";
 import AgentAlertBanner from "./agent-alert-banner";
@@ -65,6 +66,8 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
   const [pendingAttachments, setPendingAttachments] = useState<Map<string, AgentAttachment>>(new Map());
 
   const textareaRef = useRef<HTMLTextAreaElement | null>(null);
+  // Track when a turn starts to compute renderer-side latency if server usage is missing
+  const turnStartRef = useRef<number | null>(null);
 
   // Bridge provided by preload/IPC (fallback for tests/dev)
   function useApiInfo() {
@@ -100,6 +103,13 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
   const [awaitingBind, setAwaitingBind] = useState(false);
   const [queuedFirstSend, setQueuedFirstSend] = useState<string | null>(null);
 
+  // Usage telemetry state
+  type UsageRow = { id: number; session_id: string; input_tokens: number | null; output_tokens: number | null; total_tokens: number | null; latency_ms: number | null; cost_usd: number | null; created_at: number };
+  const [usageRows, setUsageRows] = useState<UsageRow[]>([]);
+  const [lastUsage, setLastUsage] = useState<UsageRow | null>(null);
+  const [provider, setProvider] = useState<string | null>(null);
+  const [modelId, setModelId] = useState<string | null>(null);
+
   // Panel enabled only when a workspace is active and a folder is selected
   const panelEnabled = useMemo<boolean>(() => {
     // Gate visually by app-level workspace presence + folder; use id for thread operations
@@ -318,9 +328,10 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
       };
       return { ...requestBody, messages, context: envelope };
     },
-    onFinish: async ({ messages: resultMessages }: { messages: unknown[] }) => {
+    onFinish: async (finishInfo: any) => {
       try {
         if (sessionId) {
+          try { console.log('[UI][Telemetry] onFinish: snapshot + usage refresh start', { sessionId }); } catch { /* noop */ }
           const [p, m] = await Promise.all([
             (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.provider' }),
             (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.defaultModel' }),
@@ -334,7 +345,7 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
               const res = await (window as any).electron?.ipcRenderer?.invoke?.('agent:threads:saveSnapshot', {
                 sessionId,
                 workspaceId: wsId || undefined,
-                messages: resultMessages,
+                messages: (finishInfo && finishInfo.messages) ? finishInfo.messages : undefined,
                 meta: { model, provider },
               });
               if (res && typeof res === 'object' && 'success' in res && (res as any).success === false) {
@@ -350,6 +361,23 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
               await new Promise((r) => setTimeout(r, 200));
             }
           }
+
+          // Renderer-side telemetry append (usage + latency)
+          try {
+            const uRoot = finishInfo && (finishInfo.usage || finishInfo.data?.usage) ? (finishInfo.usage || finishInfo.data?.usage) : null;
+            const input = (uRoot && typeof uRoot.inputTokens === 'number') ? uRoot.inputTokens : null;
+            const output = (uRoot && typeof uRoot.outputTokens === 'number') ? uRoot.outputTokens : null;
+            const total = (uRoot && typeof uRoot.totalTokens === 'number') ? uRoot.totalTokens : ((input != null && output != null) ? input + output : null);
+            const latency = (turnStartRef.current && typeof turnStartRef.current === 'number') ? (Date.now() - turnStartRef.current) : null;
+            if (input != null || output != null || total != null || latency != null) {
+              await (window as any).electron?.ipcRenderer?.invoke?.('agent:usage:append', { sessionId, inputTokens: input, outputTokens: output, totalTokens: total, latencyMs: latency });
+              try { console.log('[UI][Telemetry] renderer append usage', { sessionId, input, output, total, latency }); } catch { /* noop */ }
+            } else {
+              try { console.log('[UI][Telemetry] renderer append skipped (no usage payload)'); } catch { /* noop */ }
+            }
+          } catch (e) {
+            try { console.warn('[UI][Telemetry] renderer append failed', e); } catch { /* noop */ }
+          }
         }
       } catch { /* ignore */ }
       // Clear one-shot attachments
@@ -360,6 +388,17 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
         setErrorInfo(null);
       }
       hadErrorRef.current = false;
+      // Refresh usage immediately after finish
+      try {
+        if (sessionId) {
+          const res: any = await (window as any).electron?.ipcRenderer?.invoke?.('agent:usage:list', { sessionId });
+          if (res && res.success && Array.isArray(res.data)) {
+            setUsageRows(res.data as UsageRow[]);
+            setLastUsage((res.data as UsageRow[])[(res.data as UsageRow[]).length - 1] || null);
+            try { console.log('[UI][Telemetry] onFinish: usage refreshed', { count: (res.data as UsageRow[]).length }); } catch { /* noop */ }
+          }
+        }
+      } catch { /* ignore */ }
     },
     onError: (err: any) => {
       const code = typeof err?.status === "number" ? err.status : (typeof err?.code === "number" ? err.code : null);
@@ -421,6 +460,153 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
     }
   } as any);
 
+  // Update usage list on session change
+  useEffect(() => {
+    (async () => {
+      try {
+        if (!sessionId) { setUsageRows([]); setLastUsage(null); return; }
+        const res: any = await (window as any).electron?.ipcRenderer?.invoke?.('agent:usage:list', { sessionId });
+        if (res && res.success && Array.isArray(res.data)) {
+          setUsageRows(res.data as UsageRow[]);
+          setLastUsage((res.data as UsageRow[])[(res.data as UsageRow[]).length - 1] || null);
+          try { console.log('[UI][Telemetry] fetched usage rows', { sessionId, count: (res.data as UsageRow[]).length }); } catch { /* noop */ }
+        } else {
+          try { console.log('[UI][Telemetry] fetched usage rows: empty or error', { sessionId, result: res }); } catch { /* noop */ }
+        }
+      } catch { /* ignore */ }
+    })();
+  }, [sessionId]);
+
+  // When streaming completes, refresh last usage quickly
+  const lastStatusRef = useRef<string | null>(null);
+  useEffect(() => {
+    const prev = lastStatusRef.current;
+    lastStatusRef.current = status as string | null;
+    const finishedNow = Boolean(prev && (prev === 'streaming' || prev === 'submitted') && !(status === 'streaming' || status === 'submitted'));
+    if (finishedNow && sessionId) {
+      setTimeout(async () => {
+        try {
+          const res: any = await (window as any).electron?.ipcRenderer?.invoke?.('agent:usage:list', { sessionId });
+          if (res && res.success && Array.isArray(res.data)) {
+            setUsageRows(res.data as UsageRow[]);
+            setLastUsage((res.data as UsageRow[])[(res.data as UsageRow[]).length - 1] || null);
+            try { console.log('[UI][Telemetry] status change refresh', { prev, next: status, count: (res.data as UsageRow[]).length }); } catch { /* noop */ }
+          }
+        } catch { /* ignore */ }
+      }, 75);
+    }
+  }, [status, sessionId]);
+
+  // Fetch provider/model for cost hints
+  useEffect(() => {
+    (async () => {
+      try {
+        const [p, m] = await Promise.all([
+          (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.provider' }),
+          (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.defaultModel' }),
+        ]);
+        const prov = p && p.success && typeof p.data === 'string' ? p.data : null;
+        const mid = m && m.success && typeof m.data === 'string' ? m.data : null;
+        setProvider(prov);
+        setModelId(mid);
+        try { console.log('[UI][Telemetry] provider/model', { provider: prov, model: mid }); } catch { /* noop */ }
+      } catch { /* ignore */ }
+    })();
+  }, []);
+
+  function formatLatency(ms: number | null | undefined): string {
+    if (!ms || ms <= 0) return "—";
+    if (ms >= 1000) return `${(ms / 1000).toFixed(2)}s`;
+    return `${ms}ms`;
+  }
+
+  function formatTokens(u?: Partial<UsageRow> | null): string {
+    if (!u) return "—";
+    const i = u.input_tokens ?? null;
+    const o = u.output_tokens ?? null;
+    const t = (typeof u.total_tokens === 'number') ? u.total_tokens : ((i != null && o != null) ? (i + o) : null);
+    if (i == null && o == null && t == null) return "—";
+    if (i != null && o != null) return `${i}/${o} · ${t}`;
+    if (t != null) return `${t}`;
+    return `${i ?? '—'}/${o ?? '—'}`;
+  }
+
+  // Very rough cost hint (optional). Extend map as needed.
+  function estimateCostUSD(u?: Partial<UsageRow> | null): string | null {
+    if (!u) return null;
+    const i = u.input_tokens ?? 0;
+    const o = u.output_tokens ?? 0;
+    const t = (typeof u.total_tokens === 'number') ? u.total_tokens : (i + o);
+    if (!t) return null;
+    const m = (modelId || '').toLowerCase();
+    // Default approximate rates per 1K tokens
+    const perK: { in: number; out: number } = m.includes('gpt-4o-mini') ? { in: 0.0005, out: 0.0015 } :
+      m.includes('gpt-5') ? { in: 0.005, out: 0.015 } :
+      m.includes('haiku') ? { in: 0.0008, out: 0.0024 } : { in: 0.001, out: 0.003 };
+    const cost = (i / 1000) * perK.in + (o / 1000) * perK.out;
+    return `$${cost.toFixed(cost < 0.01 ? 3 : 2)}`;
+  }
+
+  // Log lastUsage updates for visibility
+  useEffect(() => {
+    try { console.log('[UI][Telemetry] lastUsage updated', lastUsage); } catch { /* noop */ }
+  }, [lastUsage]);
+
+  // Estimate tokens for a message index (assistant + preceding user)
+  function estimateTokensForAssistant(idx: number): { input: number | null; output: number | null; total: number | null } {
+    try {
+      const m = messages[idx];
+      if (!m || m.role !== 'assistant') return { input: null, output: null, total: null };
+      const outText = extractVisibleTextFromMessage(m);
+      const output = outText ? Math.ceil(outText.length / TOKEN_COUNTING.CHARS_PER_TOKEN) : 0;
+      // find nearest preceding user message
+      let inputText = '';
+      for (let i = idx - 1; i >= 0; i--) {
+        if (messages[i]?.role === 'user') { inputText = extractVisibleTextFromMessage(messages[i]); break; }
+      }
+      const input = inputText ? Math.ceil(inputText.length / TOKEN_COUNTING.CHARS_PER_TOKEN) : 0;
+      const total = input + output;
+      return { input, output, total };
+    } catch { return { input: null, output: null, total: null }; }
+  }
+
+  // Aggregate session totals from persisted usage; fallback to estimate from messages when needed
+  const sessionTotals = useMemo(() => {
+    // Prefer DB rows only if they actually contain any token numbers
+    try {
+      if (Array.isArray(usageRows) && usageRows.length > 0) {
+        const hasAnyToken = usageRows.some(r => (
+          (typeof r.input_tokens === 'number' && r.input_tokens > 0) ||
+          (typeof r.output_tokens === 'number' && r.output_tokens > 0) ||
+          (typeof r.total_tokens === 'number' && r.total_tokens > 0)
+        ));
+        if (hasAnyToken) {
+          let inSum = 0, outSum = 0, totalSum = 0; let approx = false; let costSum = 0; let anyCost = false;
+          for (const r of usageRows) {
+            if (r.input_tokens == null || r.output_tokens == null || r.total_tokens == null) approx = true;
+            inSum += r.input_tokens ?? 0;
+            outSum += r.output_tokens ?? 0;
+            totalSum += (typeof r.total_tokens === 'number' ? r.total_tokens : ((r.input_tokens ?? 0) + (r.output_tokens ?? 0)));
+            if (typeof r.cost_usd === 'number' && Number.isFinite(r.cost_usd)) { costSum += r.cost_usd; anyCost = true; }
+          }
+          return { inSum, outSum, totalSum, approx, costUsd: anyCost ? costSum : null } as const;
+        }
+      }
+    } catch { /* noop */ }
+    // Fallback estimation from messages: user = input; assistant = output
+    try {
+      let inSum = 0, outSum = 0;
+      for (const m of messages as any[]) {
+        const txt = extractVisibleTextFromMessage(m);
+        const t = txt ? Math.ceil(txt.length / TOKEN_COUNTING.CHARS_PER_TOKEN) : 0;
+        if (m?.role === 'user') inSum += t; else if (m?.role === 'assistant') outSum += t;
+      }
+      return { inSum, outSum, totalSum: inSum + outSum, approx: true, costUsd: null } as const;
+    } catch {
+      return { inSum: 0, outSum: 0, totalSum: 0, approx: true, costUsd: null } as const;
+    }
+  }, [usageRows, messages]);
+
   // Load last/open thread when workspace changes, but never clobber an active session
   useEffect(() => {
     let cancelled = false;
@@ -720,7 +906,8 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
       // Build final strings — attachments first, then the user's message
       const llmText = [...llmBlocks, userText].filter(Boolean).join("

");
 
-      // Send to LLM with full contents
+      // Send to LLM with full contents (mark turn start for latency)
+      try { turnStartRef.current = Date.now(); } catch { /* noop */ }
       sendMessage({ text: llmText } as any);
 
       // Clear local composer
@@ -846,6 +1033,27 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
     <div className="agent-panel" style={{ width: `${agentWidth}px` }} data-testid="agent-panel">
       <div className="agent-panel-header">
         <div className="agent-panel-title">Agent</div>
+        {/* Session totals chip */}
+        {(() => {
+          const chipInput = sessionTotals.inSum;
+          const chipOutput = sessionTotals.outSum;
+          const chipTotal = sessionTotals.totalSum;
+          const approx = sessionTotals.approx;
+          const label = `${chipTotal} ${approx ? '(approx) ' : ''}tokens (in: ${chipInput}, out: ${chipOutput})`;
+          const persistedCost = (typeof sessionTotals.costUsd === 'number' && Number.isFinite(sessionTotals.costUsd)) ? `$${sessionTotals.costUsd.toFixed(4)}` : null;
+          const estimatedCost = (!persistedCost && (chipInput > 0 || chipOutput > 0)) ? (estimateCostUSD({ input_tokens: chipInput as any, output_tokens: chipOutput as any, total_tokens: chipTotal as any } as any) || null) : null;
+          const costTxt = persistedCost || estimatedCost || null;
+          // Keep the chip visible once a conversation starts, even if totals are 0 mid-stream
+          const hasAnyMessages = Array.isArray(messages) && messages.length > 0;
+          if (!hasAnyMessages && chipTotal <= 0 && !costTxt) return null;
+          return (
+            <div className="agent-usage-chip" title={`Session totals — Input: ${chipInput}, Output: ${chipOutput}, Total: ${chipTotal}${costTxt ? `, Cost: ${costTxt}` : ''}`}>
+              <span className="dot" />
+              <span>{label}</span>
+              {costTxt && (<><span>·</span><span>{costTxt}</span></>)}
+            </div>
+          );
+        })()}
         <div style={{ marginLeft: "auto", display: "flex", gap: 8 }}>
           <button className="secondary" onClick={async () => { const ws = await resolveWorkspaceId(); if (ws) setActiveWorkspaceId(ws); setThreadsRefreshKey((x)=>x+1); setShowThreads(true); }} title="Threads" aria-label="Threads" disabled={!panelEnabled}>
             <ListIcon size={16} />
@@ -968,6 +1176,56 @@ const AgentPanel = ({ hidden, allFiles = [], selectedFolder = null, currentWorks
                   <div style={{ whiteSpace: "pre-wrap" }}>{displayText}</div>
                   {/* Minimal tool-call visualization beneath assistant messages */}
                   {m?.role === "assistant" ? <AgentToolCalls message={m} /> : null}
+                  {/* User message token count (no latency) */}
+                  {m?.role === 'user' && (() => {
+                    try {
+                      const userTok = rawText ? Math.ceil(rawText.length / TOKEN_COUNTING.CHARS_PER_TOKEN) : 0;
+                      const tip = `User message tokens: ${userTok} (approx)`;
+                      return (
+                        <div className="message-usage-row">
+                          <span className="info-icon" aria-label="User token usage">
+                            <InfoIcon size={12} />
+                            <span className="tooltip-box">{tip}</span>
+                          </span>
+                          <span>{userTok} tokens</span>
+                        </div>
+                      );
+                    } catch { return null; }
+                  })()}
+                  {/* Usage info icon with tooltip */}
+                  {(() => {
+                    if (m?.role !== 'assistant') return null;
+                    let aIdx = 0;
+                    for (let i = 0; i <= idx; i++) { if (messages[i]?.role === 'assistant') aIdx += 1; }
+                    const usageInfo = usageRows[aIdx - 1] as UsageRow | undefined;
+                    if (!usageInfo) return null;
+                    try {
+                      console.log('[UI][Telemetry] assistant usage mapping', { messageIndex: idx, assistantIndex: aIdx, usage: usageInfo });
+                    } catch { /* noop */ }
+                    // Build tooltip contents with fallbacks
+                    const approxFallback = (!usageInfo.input_tokens && !usageInfo.output_tokens && !usageInfo.total_tokens);
+                    const approx = approxFallback ? estimateTokensForAssistant(idx) : null;
+                    const inTok = usageInfo.input_tokens ?? approx?.input ?? null;
+                    const outTok = usageInfo.output_tokens ?? approx?.output ?? null;
+                    const totalTok = (typeof usageInfo.total_tokens === 'number') ? usageInfo.total_tokens : ((inTok != null && outTok != null) ? (inTok + outTok) : (approx?.total ?? null));
+                    const latencyTxt = formatLatency(usageInfo.latency_ms);
+                    const costTxt = (typeof usageInfo.cost_usd === 'number' && Number.isFinite(usageInfo.cost_usd)) ? `$${usageInfo.cost_usd.toFixed(4)}` : (estimateCostUSD(usageInfo) || null);
+                    const tooltip = `Output tokens: ${outTok ?? '—'}${approx && usageInfo.output_tokens == null ? ' (approx)' : ''}
` +
+                      `Input tokens: ${inTok ?? '—'}${approx && usageInfo.input_tokens == null ? ' (approx)' : ''}
` +
+                      `Total tokens: ${totalTok ?? '—'}${approx && usageInfo.total_tokens == null ? ' (approx)' : ''}
` +
+                      `Latency: ${latencyTxt}${costTxt ? `
Cost: ${costTxt}` : ''}`;
+                    const label = `${(outTok ?? '—')}${approx && usageInfo.output_tokens == null ? ' (approx)' : ''} tokens`;
+                    return (
+                      <div className="message-usage-row">
+                        <span className="info-icon" aria-label="Token usage details">
+                          <InfoIcon size={12} />
+                          <span className="tooltip-box">{tooltip}</span>
+                        </span>
+                        <span>{label}</span>
+                        <span>• {latencyTxt}</span>
+                      </div>
+                    );
+                  })()}
                   {/* Interruption indicator */}
                   {it && (
                     <div style={{ marginTop: 4, fontStyle: 'italic', color: '#a00' }}>User interrupted</div>
diff --git a/src/components/model-settings-modal.tsx b/src/components/model-settings-modal.tsx
index 0b89129..1ca0c05 100644
--- a/src/components/model-settings-modal.tsx
+++ b/src/components/model-settings-modal.tsx
@@ -18,6 +18,7 @@ export default function ModelSettingsModal({ isOpen, onClose, sessionId }: Props
   const [error, setError] = useState<string | null>(null);
   const [exporting, setExporting] = useState<"idle" | "saving" | "success" | "error">("idle");
   const [exportPath, setExportPath] = useState<string | null>(null);
+  const [usageStats, setUsageStats] = useState<{ totalIn: number; totalOut: number; total: number; avgLatency: number | null; totalCost: number | null } | null>(null);
 
   // OpenAI
   const [openaiInput, setOpenaiInput] = useState("");
@@ -72,6 +73,34 @@ export default function ModelSettingsModal({ isOpen, onClose, sessionId }: Props
 
   const canSave = status !== 'saving' && status !== 'testing';
 
+  // Load session usage stats when opened
+  useEffect(() => {
+    (async () => {
+      try {
+        if (!isOpen || !sessionId) { setUsageStats(null); return; }
+        const res: any = await (window as any).electron?.ipcRenderer?.invoke?.('agent:usage:list', { sessionId });
+        if (res && res.success && Array.isArray(res.data)) {
+          const rows = res.data as Array<{ input_tokens: number | null; output_tokens: number | null; total_tokens: number | null; latency_ms: number | null; cost_usd: number | null }>;
+          let inSum = 0, outSum = 0, totalSum = 0;
+          let latSum = 0, latCount = 0;
+          let costSum = 0, costCount = 0;
+          for (const r of rows) {
+            inSum += r.input_tokens ?? 0;
+            outSum += r.output_tokens ?? 0;
+            totalSum += (typeof r.total_tokens === 'number' ? r.total_tokens : ((r.input_tokens ?? 0) + (r.output_tokens ?? 0)));
+            if (typeof r.latency_ms === 'number') { latSum += r.latency_ms; latCount += 1; }
+            if (typeof r.cost_usd === 'number' && Number.isFinite(r.cost_usd)) { costSum += r.cost_usd; costCount += 1; }
+          }
+          setUsageStats({ totalIn: inSum, totalOut: outSum, total: totalSum, avgLatency: latCount > 0 ? Math.round(latSum / latCount) : null, totalCost: costCount > 0 ? costSum : null });
+          try { console.log('[UI][Telemetry] settings: usage stats', { sessionId, rows: rows.length, totalIn: inSum, totalOut: outSum, total: totalSum, avgLatency: latCount > 0 ? Math.round(latSum / latCount) : null }); } catch { /* noop */ }
+        } else {
+          setUsageStats(null);
+          try { console.log('[UI][Telemetry] settings: no usage stats', { sessionId, res }); } catch { /* noop */ }
+        }
+      } catch { setUsageStats(null); }
+    })();
+  }, [isOpen, sessionId]);
+
   async function saveKey(key: string, value: string | null, enc = true) {
     setStatus('saving');
     setError(null);
@@ -239,6 +268,27 @@ export default function ModelSettingsModal({ isOpen, onClose, sessionId }: Props
               </div>
             </section>
 
+            {usageStats && (
+              <section className="settings-section">
+                <div className="settings-grid">
+                  <div className="field">
+                    <label>Session Tokens</label>
+                    <div style={{ fontSize: 12, color: "var(--text-secondary)" }}>
+                      Input: {usageStats.totalIn.toLocaleString()} · Output: {usageStats.totalOut.toLocaleString()} · Total: {usageStats.total.toLocaleString()}
+                    </div>
+                  </div>
+                  <div className="field">
+                    <label>Average Latency</label>
+                    <div style={{ fontSize: 12, color: "var(--text-secondary)" }}>{usageStats.avgLatency != null ? (usageStats.avgLatency >= 1000 ? `${(usageStats.avgLatency/1000).toFixed(2)}s` : `${usageStats.avgLatency}ms`) : '—'}</div>
+                  </div>
+                  <div className="field">
+                    <label>Session Cost</label>
+                    <div style={{ fontSize: 12, color: "var(--text-secondary)" }}>{usageStats.totalCost != null ? `$${usageStats.totalCost.toFixed(4)}` : '—'}</div>
+                  </div>
+                </div>
+              </section>
+            )}
+
             <section className="settings-section">
               <div className="actions">
                 <button
diff --git a/src/main/__tests__/api-chat-errors.test.ts b/src/main/__tests__/api-chat-errors.test.ts
index 200deea..e60ab6f 100644
--- a/src/main/__tests__/api-chat-errors.test.ts
+++ b/src/main/__tests__/api-chat-errors.test.ts
@@ -22,6 +22,11 @@ jest.mock("ai", () => {
 });
 
 jest.mock("@ai-sdk/openai", () => ({ openai: () => ({ id: "test-model" }) }));
+// Mock broadcast helper to avoid import.meta parsing under Jest
+jest.mock("../../main/broadcast-helper", () => ({
+  broadcastToRenderers: jest.fn(),
+  broadcastWorkspaceUpdated: jest.fn(),
+}));
 
 describe("handleChat error classification", () => {
   it("returns 503 AI_PROVIDER_CONFIG for missing API key errors", async () => {
diff --git a/src/main/__tests__/api-chat-invalid-model.test.ts b/src/main/__tests__/api-chat-invalid-model.test.ts
index 0863603..17b877d 100644
--- a/src/main/__tests__/api-chat-invalid-model.test.ts
+++ b/src/main/__tests__/api-chat-invalid-model.test.ts
@@ -25,6 +25,11 @@ jest.mock("ai", () => {
 });
 
 jest.mock("@ai-sdk/openai", () => ({ openai: () => ({ id: "test-model" }) }));
+// Mock broadcast helper to avoid import.meta parsing under Jest
+jest.mock("../../main/broadcast-helper", () => ({
+  broadcastToRenderers: jest.fn(),
+  broadcastWorkspaceUpdated: jest.fn(),
+}));
 
 describe("handleChat invalid model classification", () => {
   it("returns 400 AI_INVALID_MODEL for unknown model errors", async () => {
diff --git a/src/main/__tests__/api-chat-retry-invalid-tool.test.ts b/src/main/__tests__/api-chat-retry-invalid-tool.test.ts
index bcf6293..b5d617b 100644
--- a/src/main/__tests__/api-chat-retry-invalid-tool.test.ts
+++ b/src/main/__tests__/api-chat-retry-invalid-tool.test.ts
@@ -25,6 +25,11 @@ jest.mock("ai", () => {
 });
 
 jest.mock("@ai-sdk/openai", () => ({ openai: () => ({ id: "test-model" }) }));
+// Mock broadcast helper to avoid import.meta parsing under Jest
+jest.mock("../../main/broadcast-helper", () => ({
+  broadcastToRenderers: jest.fn(),
+  broadcastWorkspaceUpdated: jest.fn(),
+}));
 
 describe("handleChat retries without tools on invalid_function_parameters", () => {
   it("retries once without tools and streams", async () => {
@@ -56,4 +61,3 @@ describe("handleChat retries without tools on invalid_function_parameters", () =
     expect(second.tools).toBeUndefined();
   });
 });
-
diff --git a/src/main/__tests__/api-chat-tools.test.ts b/src/main/__tests__/api-chat-tools.test.ts
index 0ad7442..f7142c5 100644
--- a/src/main/__tests__/api-chat-tools.test.ts
+++ b/src/main/__tests__/api-chat-tools.test.ts
@@ -23,12 +23,17 @@ jest.mock("ai", () => {
 });
 
 jest.mock("@ai-sdk/openai", () => ({ openai: () => ({ id: "test-model" }) }));
+// Mock broadcast helper to avoid import.meta parsing under Jest
+jest.mock("../../main/broadcast-helper", () => ({
+  broadcastToRenderers: jest.fn(),
+  broadcastWorkspaceUpdated: jest.fn(),
+}));
 
 describe("handleChat tools wiring", () => {
   it("passes a tools registry to streamText", async () => {
     const handlers = new APIRouteHandlers(dbStub, previewProxyStub, previewControllerStub as any);
-    const req: any = { body: { messages: [], context: undefined } };
-    const res: any = { status: jest.fn(() => res), json: jest.fn(() => res), end: jest.fn() };
+    const req: any = { body: { messages: [], context: undefined }, on: jest.fn() };
+    const res: any = { status: jest.fn(() => res), json: jest.fn(() => res), end: jest.fn(), on: jest.fn() };
 
     const { streamText } = require("ai");
     await handlers.handleChat(req, res);
diff --git a/src/main/__tests__/api-chat-usage-telemetry.test.ts b/src/main/__tests__/api-chat-usage-telemetry.test.ts
new file mode 100644
index 0000000..a7229ef
--- /dev/null
+++ b/src/main/__tests__/api-chat-usage-telemetry.test.ts
@@ -0,0 +1,101 @@
+import { APIRouteHandlers } from "../../main/api-route-handlers";
+
+// Mock ai SDK and openai
+jest.mock("ai", () => {
+  const stream = jest.fn();
+  return {
+    streamText: stream,
+    convertToModelMessages: jest.fn((msgs: any) => msgs),
+    consumeStream: jest.fn(),
+    tool: (def: any) => def,
+    jsonSchema: (schema: any) => ({ jsonSchema: schema, validate: async (v: any) => ({ success: true, value: v }) }),
+  };
+});
+
+jest.mock("@ai-sdk/openai", () => ({ openai: () => ({ id: "test-model" }) }));
+// Mock broadcast helper to avoid import.meta.url parsing in Node CJS under Jest
+jest.mock("../../main/broadcast-helper", () => ({
+  broadcastToRenderers: jest.fn(),
+  broadcastWorkspaceUpdated: jest.fn(),
+}));
+
+describe("handleChat usage telemetry onFinish", () => {
+  it("persists usage with latency via insertUsageSummaryWithLatency when available", async () => {
+    const insertWithLatency = jest.fn(async () => {});
+    const insertLegacy = jest.fn(async () => {});
+    const dbStub: any = {
+      getPreference: async () => null,
+      getWorkspace: async () => null,
+      upsertChatSession: async () => {},
+      insertUsageSummary: insertLegacy,
+      insertUsageSummaryWithLatency: insertWithLatency,
+      insertToolExecution: async () => {},
+      listToolExecutions: async () => [],
+    };
+
+    const handlers = new APIRouteHandlers(dbStub, {} as any, {} as any);
+    const req: any = { body: { messages: [{ role: 'user', content: [{ type: 'text', text: 'hi' }] }] }, headers: {}, on: jest.fn() };
+    const res: any = { status: jest.fn(() => res), json: jest.fn(() => res), end: jest.fn(), on: jest.fn() };
+
+    const { streamText } = require("ai");
+    (streamText as jest.Mock).mockImplementationOnce((opts: any) => {
+      // Simulate finish callback with usage provided by provider
+      setTimeout(() => {
+        opts?.onFinish?.({ usage: { inputTokens: 12, outputTokens: 34, totalTokens: 46 } });
+      }, 0);
+      return {
+        pipeUIMessageStreamToResponse: (resp: any) => resp.status(200).end(),
+      };
+    });
+
+    await handlers.handleChat(req, res);
+
+    // Allow microtasks to run the onFinish callback
+    await new Promise((r) => setTimeout(r, 1));
+
+    expect(streamText).toHaveBeenCalledTimes(1);
+    expect(insertWithLatency).toHaveBeenCalledTimes(1);
+    const args = insertWithLatency.mock.calls[0] as any[];
+    // args: sessionId, input, output, total, latency
+    expect(args[1]).toBe(12);
+    expect(args[2]).toBe(34);
+    expect(args[3]).toBe(46);
+    expect(typeof args[4] === 'number' || args[4] === null).toBe(true);
+    expect(insertLegacy).not.toHaveBeenCalled();
+  });
+
+  it("falls back to legacy insert when latency method not present", async () => {
+    const insertLegacy = jest.fn(async () => {});
+    const dbStub: any = {
+      getPreference: async () => null,
+      getWorkspace: async () => null,
+      upsertChatSession: async () => {},
+      insertUsageSummary: insertLegacy,
+      insertToolExecution: async () => {},
+    };
+
+    const handlers = new APIRouteHandlers(dbStub, {} as any, {} as any);
+    const req: any = { body: { messages: [{ role: 'user', content: [{ type: 'text', text: 'hi' }] }] }, headers: {}, on: jest.fn() };
+    const res: any = { status: jest.fn(() => res), json: jest.fn(() => res), end: jest.fn(), on: jest.fn() };
+
+    const { streamText } = require("ai");
+    ;(streamText as jest.Mock).mockImplementationOnce((opts: any) => {
+      setTimeout(() => {
+        // Provide only input/output to test total fallback computation
+        opts?.onFinish?.({ usage: { inputTokens: 3, outputTokens: 7 } });
+      }, 0);
+      return {
+        pipeUIMessageStreamToResponse: (resp: any) => resp.status(200).end(),
+      };
+    });
+
+    await handlers.handleChat(req, res);
+    await new Promise((r) => setTimeout(r, 1));
+
+    expect(insertLegacy).toHaveBeenCalledTimes(1);
+    const args = insertLegacy.mock.calls[0] as any[];
+    expect(args[1]).toBe(3);
+    expect(args[2]).toBe(7);
+    expect(args[3]).toBe(10); // computed total
+  });
+});
diff --git a/src/main/agent/model-resolver.ts b/src/main/agent/model-resolver.ts
index 310f06c..2f84c92 100644
--- a/src/main/agent/model-resolver.ts
+++ b/src/main/agent/model-resolver.ts
@@ -6,7 +6,7 @@ import { getStaticModels } from "./models-catalog";
 
 import { createOpenAI, openai as openaiDirect } from "@ai-sdk/openai";
 import { createAnthropic, anthropic as anthropicDirect } from "@ai-sdk/anthropic";
-import type { LanguageModelV1 } from "ai";
+import type { LanguageModel } from "ai";
 
 type DbGetter = { getPreference: (k: string) => Promise<unknown> };
 
@@ -49,7 +49,7 @@ export type ResolveModelInput = {
   modelId: string;
 };
 
-export async function resolveModelForRequest(input: ResolveModelInput): Promise<{ model: LanguageModelV1 }>
+export async function resolveModelForRequest(input: ResolveModelInput): Promise<{ model: LanguageModel }>
 {
   const { db, provider } = input;
   const modelId = canonicalizeModelId(provider, input.modelId);
@@ -57,34 +57,38 @@ export async function resolveModelForRequest(input: ResolveModelInput): Promise<
 
   if (provider === "openai") {
     const key = creds.openai?.apiKey || process.env.OPENAI_API_KEY || null;
-    if (key) {
+    if (key && typeof createOpenAI === 'function') {
       const client = createOpenAI({ apiKey: key });
-      return { model: client(modelId) };
+      return { model: client(modelId) } as any;
     }
-    // Fall back to env-only direct helper
-    return { model: openaiDirect(modelId) };
+    // Fall back to env-only direct helper or when createOpenAI is unavailable in test/mocks
+    return { model: openaiDirect(modelId) } as any;
   }
 
   if (provider === "anthropic") {
     const key = creds.anthropic?.apiKey || process.env.ANTHROPIC_API_KEY || null;
-    if (key) {
+    if (key && typeof createAnthropic === 'function') {
       const client = createAnthropic({ apiKey: key });
-      return { model: client(modelId) };
+      return { model: client(modelId) } as any;
     }
-    // Fall back to env only
+    // Fall back to env only or when createAnthropic is unavailable
     return { model: anthropicDirect(modelId) } as any;
   }
 
   if (provider === "openrouter") {
     const key = creds.openrouter?.apiKey || null;
     const baseURL = creds.openrouter?.baseUrl || "https://openrouter.ai/api/v1";
+    if (typeof createOpenAI !== 'function') {
+      // Fallback to direct helper when createOpenAI is not available in mocks/tests
+      return { model: openaiDirect(modelId) } as any;
+    }
     if (!key) {
       // Return an OpenAI client without key to force validation errors on use
       const client = createOpenAI({ apiKey: "" as unknown as string, baseURL });
-      return { model: client(modelId) };
+      return { model: client(modelId) } as any;
     }
     const client = createOpenAI({ apiKey: key, baseURL });
-    return { model: client(modelId) };
+    return { model: client(modelId) } as any;
   }
 
   // Unknown provider: attempt OpenAI as default
diff --git a/src/main/agent/pricing.ts b/src/main/agent/pricing.ts
new file mode 100644
index 0000000..a9f6538
--- /dev/null
+++ b/src/main/agent/pricing.ts
@@ -0,0 +1,79 @@
+import type { ProviderId } from "./models-catalog";
+
+// Prices are per 1,000,000 tokens (per-million) to match vendor docs
+export type Pricing = {
+  inPerMTok: number; // USD per 1M input tokens
+  outPerMTok: number; // USD per 1M output tokens
+  cacheWritePerMTok?: number;
+  cacheReadPerMTok?: number;
+  thinkPerMTok?: number; // e.g., "reasoning"/"thinking" tokens for some providers
+  subscriptionFree?: boolean; // when usage is covered by subscription (set cost=0)
+};
+
+// Minimal seed catalog covering common defaults used by PasteFlow out of the box
+// Extend over time as needed; unknown models return null for cost (UI can show "—")
+const PRICING: Record<string, Pricing> = {
+  // OpenAI
+  "openai:gpt-4o-mini": { inPerMTok: 5, outPerMTok: 15 },
+  "openai:gpt-5": { inPerMTok: 10, outPerMTok: 30 }, // placeholder conservative
+  "openai:gpt-5-mini": { inPerMTok: 6, outPerMTok: 18 }, // placeholder conservative
+
+  // Anthropic
+  "anthropic:claude-3-5-haiku-20241022": { inPerMTok: 3, outPerMTok: 15, cacheReadPerMTok: 0.3, cacheWritePerMTok: 3.75 },
+  "anthropic:claude-sonnet-4-20250514": { inPerMTok: 3, outPerMTok: 15, cacheReadPerMTok: 0.3, cacheWritePerMTok: 3.75 },
+
+  // OpenRouter routes (map to underlying OpenAI pricing as a safe approximation)
+  "openrouter:openai/gpt-4o-mini": { inPerMTok: 5, outPerMTok: 15 },
+};
+
+export type CostInput = {
+  inputTokens?: number | null;
+  outputTokens?: number | null;
+  cacheWriteTokens?: number | null;
+  cacheReadTokens?: number | null;
+  thinkingTokens?: number | null;
+};
+
+function keyFor(provider: ProviderId, modelId: string): string {
+  return `${provider}:${modelId}`.toLowerCase();
+}
+
+export function calculateCostUSD(
+  provider: ProviderId,
+  modelId: string,
+  usage: CostInput
+): number | null {
+  try {
+    const k = keyFor(provider, modelId);
+    const p = PRICING[k];
+    if (!p) return null;
+    if (p.subscriptionFree) return 0;
+
+    const inTok = Math.max(0, Number(usage.inputTokens || 0));
+    const outTok = Math.max(0, Number(usage.outputTokens || 0));
+    const cwTok = Math.max(0, Number(usage.cacheWriteTokens || 0));
+    const crTok = Math.max(0, Number(usage.cacheReadTokens || 0));
+    const thTok = Math.max(0, Number(usage.thinkingTokens || 0));
+
+    // OpenAI-like rule: subtract cached reads/writes from input for "uncached" input cost when both are present
+    let uncachedIn = inTok;
+    if ((cwTok > 0 || crTok > 0) && inTok > 0) {
+      const subtract = Math.min(inTok, cwTok + crTok);
+      uncachedIn = Math.max(0, inTok - subtract);
+    }
+
+    const inCost = p.inPerMTok * (uncachedIn / 1_000_000);
+    const outCost = p.outPerMTok * (outTok / 1_000_000);
+    const cwCost = p.cacheWritePerMTok ? p.cacheWritePerMTok * (cwTok / 1_000_000) : 0;
+    const crCost = p.cacheReadPerMTok ? p.cacheReadPerMTok * (crTok / 1_000_000) : 0;
+    const thCost = p.thinkPerMTok ? p.thinkPerMTok * (thTok / 1_000_000) : 0;
+
+    const total = inCost + outCost + cwCost + crCost + thCost;
+    // Guard against denormals; round to cents precision but return full float
+    if (!Number.isFinite(total) || total < 0) return null;
+    return total;
+  } catch {
+    return null;
+  }
+}
+
diff --git a/src/main/api-route-handlers.ts b/src/main/api-route-handlers.ts
index 7f838ed..1cf86d1 100644
--- a/src/main/api-route-handlers.ts
+++ b/src/main/api-route-handlers.ts
@@ -280,7 +280,8 @@ export class APIRouteHandlers {
       }
 
       // Derive or generate session id
-      const headerSession = String((req.headers['x-pasteflow-session'] || req.headers['x-pf-session-id'] || '')).trim();
+      const _headers = (req.headers ?? {}) as Record<string, unknown>;
+      const headerSession = String(((_headers['x-pasteflow-session'] as unknown) || (_headers['x-pf-session-id'] as unknown) || '')).trim();
       const sessionId = parsed.data.sessionId || (headerSession || randomUUID());
 
       // Sanitize/normalize context
@@ -386,6 +387,7 @@ export class APIRouteHandlers {
         } catch { /* noop */ }
       }
 
+      const start = Date.now();
       const result = streamText({
         model,
         system,
@@ -397,6 +399,39 @@ export class APIRouteHandlers {
         onAbort: () => {
           // Best-effort: nothing to persist yet; hook kept for future telemetry
         },
+        onFinish: async (info: any) => {
+          try {
+            const u = (info && typeof info === 'object' && (info as any).usage) ? (info as any).usage : (info ?? {});
+            const input = (u && typeof u.inputTokens === 'number') ? u.inputTokens : null;
+            const output = (u && typeof u.outputTokens === 'number') ? u.outputTokens : null;
+            const total = (u && typeof u.totalTokens === 'number') ? u.totalTokens : (
+              (input != null && output != null) ? (input + output) : null
+            );
+            const latency = Date.now() - start;
+            // Compute cost (best-effort) on server using simple pricing table
+            let cost: number | null = null;
+            try {
+              const { calculateCostUSD } = await import('./agent/pricing');
+              const modelIdForPricing = String(cfg.DEFAULT_MODEL || "");
+              cost = calculateCostUSD(provider, modelIdForPricing, { inputTokens: input ?? undefined, outputTokens: output ?? undefined });
+            } catch { /* noop */ }
+            // Dev log for quick verification
+            try {
+              if (process.env.NODE_ENV === 'development') {
+                // eslint-disable-next-line no-console
+                console.log('[AI][finish]', { input, output, total, latency, cost });
+              }
+            } catch { /* noop */ }
+            const bridge: any = this.db as any;
+            if (typeof bridge.insertUsageSummaryWithLatencyAndCost === 'function') {
+              await bridge.insertUsageSummaryWithLatencyAndCost(sessionId, input, output, total, latency, cost ?? null);
+            } else if (typeof bridge.insertUsageSummaryWithLatency === 'function') {
+              await bridge.insertUsageSummaryWithLatency(sessionId, input, output, total, latency);
+            } else {
+              await this.db.insertUsageSummary(sessionId, input, output, total);
+            }
+          } catch { /* ignore persistence errors */ }
+        },
       });
 
       // Pipe to Express response with UI_MESSAGE_STREAM headers and consume stream on abort to avoid hangs
@@ -413,10 +448,7 @@ export class APIRouteHandlers {
         await this.db.upsertChatSession(sessionId, msgJson, ws ? String(ws.id) : null);
       } catch { /* ignore */ }
 
-      // Best-effort write a usage row (token metrics TBD)
-      try {
-        await this.db.insertUsageSummary(sessionId, null, null, null);
-      } catch { /* noop */ }
+      // Usage persistence occurs in onFinish callback; no placeholder insert here
     } catch (error) {
       // Graceful fallback: invalid tool parameter schema → retry once without tools
       if (this.isInvalidToolParametersError(error)) {
@@ -442,7 +474,8 @@ export class APIRouteHandlers {
             return res.status(400).json(toApiError('VALIDATION_ERROR', 'Invalid chat messages format'));
           }
 
-          const headerSession = String((req.headers['x-pasteflow-session'] || req.headers['x-pf-session-id'] || '')).trim();
+          const _headers = (req.headers ?? {}) as Record<string, unknown>;
+          const headerSession = String(((_headers['x-pasteflow-session'] as unknown) || (_headers['x-pf-session-id'] as unknown) || '')).trim();
           const sessionId = parsed.data.sessionId || (headerSession || randomUUID());
 
           const envelope = parsed.data.context;
@@ -466,6 +499,7 @@ export class APIRouteHandlers {
           const provider: ProviderId = cfg.PROVIDER || 'openai';
           const { model } = await resolveModelForRequest({ db: this.db as unknown as { getPreference: (k: string) => Promise<unknown> }, provider, modelId: cfg.DEFAULT_MODEL });
 
+          const start = Date.now();
           const result = streamText({
             model,
             system,
@@ -475,6 +509,37 @@ export class APIRouteHandlers {
             maxOutputTokens: cfg.MAX_OUTPUT_TOKENS ?? undefined,
             abortSignal: controller.signal,
             onAbort: () => {},
+            onFinish: async (info: any) => {
+              try {
+                const u = (info && typeof info === 'object' && (info as any).usage) ? (info as any).usage : (info ?? {});
+                const input = (u && typeof u.inputTokens === 'number') ? u.inputTokens : null;
+                const output = (u && typeof u.outputTokens === 'number') ? u.outputTokens : null;
+                const total = (u && typeof u.totalTokens === 'number') ? u.totalTokens : (
+                  (input != null && output != null) ? (input + output) : null
+                );
+                const latency = Date.now() - start;
+                let cost: number | null = null;
+                try {
+                  const { calculateCostUSD } = await import('./agent/pricing');
+                  const modelIdForPricing = String(cfg.DEFAULT_MODEL || "");
+                  cost = calculateCostUSD(provider, modelIdForPricing, { inputTokens: input ?? undefined, outputTokens: output ?? undefined });
+                } catch { /* noop */ }
+                try {
+                  if (process.env.NODE_ENV === 'development') {
+                    // eslint-disable-next-line no-console
+                    console.log('[AI][finish:fallback]', { input, output, total, latency, cost });
+                  }
+                } catch { /* noop */ }
+                const bridge: any = this.db as any;
+                if (typeof bridge.insertUsageSummaryWithLatencyAndCost === 'function') {
+                  await bridge.insertUsageSummaryWithLatencyAndCost(sessionId, input, output, total, latency, cost ?? null);
+                } else if (typeof bridge.insertUsageSummaryWithLatency === 'function') {
+                  await bridge.insertUsageSummaryWithLatency(sessionId, input, output, total, latency);
+                } else {
+                  await this.db.insertUsageSummary(sessionId, input, output, total);
+                }
+              } catch { /* ignore persistence errors */ }
+            },
           });
 
           result.pipeUIMessageStreamToResponse(res, { consumeSseStream: consumeStream });
@@ -487,9 +552,7 @@ export class APIRouteHandlers {
             await this.db.upsertChatSession(sessionId, msgJson, ws ? String(ws.id) : null);
           } catch { /* ignore */ }
 
-          try {
-            await this.db.insertUsageSummary(sessionId, null, null, null);
-          } catch { /* noop */ }
+          // Usage persistence occurs in onFinish callback; no placeholder insert here
 
           return; // streamed response
         } catch (fallbackError) {
diff --git a/src/main/db/database-bridge.ts b/src/main/db/database-bridge.ts
index edfab02..09e177d 100644
--- a/src/main/db/database-bridge.ts
+++ b/src/main/db/database-bridge.ts
@@ -277,6 +277,27 @@ export class DatabaseBridge {
     return (this.db as any).insertUsageSummary(sessionId, inputTokens, outputTokens, totalTokens);
   }
 
+  async insertUsageSummaryWithLatency(sessionId: string, inputTokens: number | null, outputTokens: number | null, totalTokens: number | null, latencyMs: number | null) {
+    if (!this.db) throw new Error('Database not initialized');
+    const impl: any = this.db as any;
+    if (typeof impl.insertUsageSummaryWithLatency === 'function') {
+      return impl.insertUsageSummaryWithLatency(sessionId, inputTokens, outputTokens, totalTokens, latencyMs);
+    }
+    return impl.insertUsageSummary(sessionId, inputTokens, outputTokens, totalTokens);
+  }
+
+  async insertUsageSummaryWithLatencyAndCost(sessionId: string, inputTokens: number | null, outputTokens: number | null, totalTokens: number | null, latencyMs: number | null, costUsd: number | null) {
+    if (!this.db) throw new Error('Database not initialized');
+    const impl: any = this.db as any;
+    if (typeof impl.insertUsageSummaryWithLatencyAndCost === 'function') {
+      return impl.insertUsageSummaryWithLatencyAndCost(sessionId, inputTokens, outputTokens, totalTokens, latencyMs, costUsd);
+    }
+    if (typeof impl.insertUsageSummaryWithLatency === 'function') {
+      return impl.insertUsageSummaryWithLatency(sessionId, inputTokens, outputTokens, totalTokens, latencyMs);
+    }
+    return impl.insertUsageSummary(sessionId, inputTokens, outputTokens, totalTokens);
+  }
+
   async listUsageSummaries(sessionId: string) {
     if (!this.db) throw new Error('Database not initialized');
     return (this.db as any).listUsageSummaries(sessionId);
diff --git a/src/main/db/database-implementation.ts b/src/main/db/database-implementation.ts
index 3ee1bf0..719c007 100644
--- a/src/main/db/database-implementation.ts
+++ b/src/main/db/database-implementation.ts
@@ -273,6 +273,32 @@ export class PasteFlowDatabase {
       maxRetries: 3
     });
 
+    // Optional migration: add latency_ms column to usage_summary if missing
+    await executeWithRetry(async () => {
+      try {
+        const columns = this.db!.prepare("PRAGMA table_info('usage_summary')").all() as Array<{ name: string }>; 
+        const hasLatency = Array.isArray(columns) && columns.some((c) => String(c.name).toLowerCase() === 'latency_ms');
+        if (!hasLatency) {
+          this.db!.exec("ALTER TABLE usage_summary ADD COLUMN latency_ms INTEGER");
+        }
+      } catch {
+        // ignore migration errors to avoid blocking startup
+      }
+    }, { operation: 'migrate_usage_summary_latency_ms', maxRetries: 1 });
+
+    // Optional migration: add cost_usd column to usage_summary if missing
+    await executeWithRetry(async () => {
+      try {
+        const columns = this.db!.prepare("PRAGMA table_info('usage_summary')").all() as Array<{ name: string }>;
+        const hasCost = Array.isArray(columns) && columns.some((c) => String(c.name).toLowerCase() === 'cost_usd');
+        if (!hasCost) {
+          this.db!.exec("ALTER TABLE usage_summary ADD COLUMN cost_usd REAL");
+        }
+      } catch {
+        // ignore migration errors
+      }
+    }, { operation: 'migrate_usage_summary_cost_usd', maxRetries: 1 });
+
     // Prepare statements with retry
     await this.prepareStatements();
   }
@@ -412,10 +438,25 @@ export class PasteFlowDatabase {
     `);
   }
 
+  // Insert that includes latency (nullable)
+  private stmtInsertUsageSummaryWithLatency(): BetterSqlite3.Statement<[
+    string,
+    number | null,
+    number | null,
+    number | null,
+    number | null,
+  ]> {
+    if (!this.db) throw new Error('DB not initialized');
+    return this.db.prepare(`
+      INSERT INTO usage_summary (session_id, input_tokens, output_tokens, total_tokens, latency_ms)
+      VALUES (?, ?, ?, ?, ?)
+    `);
+  }
+
   private stmtListUsageSummaries(): BetterSqlite3.Statement<[string]> {
     if (!this.db) throw new Error('DB not initialized');
     return this.db.prepare(`
-      SELECT id, session_id, input_tokens, output_tokens, total_tokens, created_at
+      SELECT id, session_id, input_tokens, output_tokens, total_tokens, latency_ms, cost_usd, created_at
       FROM usage_summary WHERE session_id = ? ORDER BY created_at ASC
     `);
   }
@@ -631,7 +672,45 @@ export class PasteFlowDatabase {
     }, { operation: 'insert_usage_summary' });
   }
 
-  async listUsageSummaries(sessionId: string): Promise<Array<{ id: number; session_id: string; input_tokens: number | null; output_tokens: number | null; total_tokens: number | null; created_at: number }>> {
+  async insertUsageSummaryWithLatency(sessionId: string, inputTokens: number | null, outputTokens: number | null, totalTokens: number | null, latencyMs: number | null): Promise<void> {
+    this.ensureInitialized();
+    await executeWithRetry(async () => {
+      try {
+        this.stmtInsertUsageSummaryWithLatency().run(
+          sessionId,
+          inputTokens ?? null,
+          outputTokens ?? null,
+          totalTokens ?? null,
+          latencyMs ?? null,
+        );
+      } catch {
+        // Fallback to legacy insert if statement fails (e.g., column missing)
+        this.stmtInsertUsageSummary().run(sessionId, inputTokens ?? null, outputTokens ?? null, totalTokens ?? null);
+      }
+    }, { operation: 'insert_usage_summary_with_latency' });
+  }
+
+  async insertUsageSummaryWithLatencyAndCost(sessionId: string, inputTokens: number | null, outputTokens: number | null, totalTokens: number | null, latencyMs: number | null, costUsd: number | null): Promise<void> {
+    this.ensureInitialized();
+    await executeWithRetry(async () => {
+      try {
+        // Ensure statement exists; prepare dynamically to guard against older DBs
+        const stmt = this.db!.prepare(`
+          INSERT INTO usage_summary (session_id, input_tokens, output_tokens, total_tokens, latency_ms, cost_usd)
+          VALUES (?, ?, ?, ?, ?, ?)
+        `);
+        stmt.run(sessionId, inputTokens ?? null, outputTokens ?? null, totalTokens ?? null, latencyMs ?? null, (typeof costUsd === 'number' && Number.isFinite(costUsd)) ? costUsd : null);
+      } catch {
+        try {
+          this.stmtInsertUsageSummaryWithLatency().run(sessionId, inputTokens ?? null, outputTokens ?? null, totalTokens ?? null, latencyMs ?? null);
+        } catch {
+          this.stmtInsertUsageSummary().run(sessionId, inputTokens ?? null, outputTokens ?? null, totalTokens ?? null);
+        }
+      }
+    }, { operation: 'insert_usage_summary_with_latency_and_cost' });
+  }
+
+  async listUsageSummaries(sessionId: string): Promise<Array<{ id: number; session_id: string; input_tokens: number | null; output_tokens: number | null; total_tokens: number | null; latency_ms: number | null; created_at: number }>> {
     this.ensureInitialized();
     const result = await executeWithRetry(async () => {
       const rows = this.stmtListUsageSummaries().all(sessionId) as any[];
diff --git a/src/main/main.ts b/src/main/main.ts
index 596009c..cdd683a 100644
--- a/src/main/main.ts
+++ b/src/main/main.ts
@@ -986,6 +986,52 @@ ipcMain.handle('agent:get-history', async (_e, params: unknown) => {
   }
 });
 
+// List usage summaries for a session (tokens + optional latency)
+ipcMain.handle('agent:usage:list', async (_e, params: unknown) => {
+  try {
+    const p = (params || {}) as { sessionId?: string };
+    const sessionId = typeof p.sessionId === 'string' && p.sessionId.trim() ? p.sessionId.trim() : null;
+    if (!sessionId) return { success: false, error: 'INVALID_PARAMS' };
+    if (!database || !(database as any).initialized) return { success: false, error: 'DB_NOT_INITIALIZED' };
+    const rows = await database!.listUsageSummaries(sessionId);
+    try {
+      // eslint-disable-next-line no-console
+      console.log('[Main][Telemetry] agent:usage:list', { sessionId, rows: Array.isArray(rows) ? rows.length : 0 });
+    } catch { /* noop */ }
+    return { success: true, data: rows };
+  } catch (error: unknown) {
+    return { success: false, error: (error as Error)?.message || String(error) };
+  }
+});
+
+// Append a usage row (renderer-provided fallback)
+ipcMain.handle('agent:usage:append', async (_e, params: unknown) => {
+  try {
+    const p = (params || {}) as { sessionId?: string; inputTokens?: number | null; outputTokens?: number | null; totalTokens?: number | null; latencyMs?: number | null };
+    const sessionId = typeof p.sessionId === 'string' && p.sessionId.trim() ? p.sessionId.trim() : null;
+    if (!sessionId) return { success: false, error: 'INVALID_PARAMS' };
+    if (!database || !(database as any).initialized) return { success: false, error: 'DB_NOT_INITIALIZED' };
+    const input = (typeof p.inputTokens === 'number') ? p.inputTokens : null;
+    const output = (typeof p.outputTokens === 'number') ? p.outputTokens : null;
+    const total = (typeof p.totalTokens === 'number') ? p.totalTokens : ((input != null && output != null) ? (input + output) : null);
+    const latency = (typeof p.latencyMs === 'number') ? p.latencyMs : null;
+    try {
+      if ((database as any).insertUsageSummaryWithLatency) {
+        await (database as any).insertUsageSummaryWithLatency(sessionId, input, output, total, latency);
+      } else {
+        await (database as any).insertUsageSummary(sessionId, input, output, total);
+      }
+      // eslint-disable-next-line no-console
+      console.log('[Main][Telemetry] agent:usage:append', { sessionId, input, output, total, latency });
+      return { success: true };
+    } catch (err) {
+      return { success: false, error: (err as Error)?.message || 'DB_WRITE_FAILED' };
+    }
+  } catch (error: unknown) {
+    return { success: false, error: (error as Error)?.message || String(error) };
+  }
+});
+
 ipcMain.handle('agent:execute-tool', async (_e, params: unknown) => {
   try {
     const { AgentExecuteToolSchema } = await import('./ipc/schemas');
```

## Unstaged Changes

```diff

```
