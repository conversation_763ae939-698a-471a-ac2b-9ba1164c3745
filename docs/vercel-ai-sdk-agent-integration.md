# Vercel AI SDK 5 Agent Integration for PasteFlow

## Executive Summary

This document outlines implementing an LLM coding agent directly within PasteFlow using Vercel AI SDK 5. The integration features a **dual-context system**: the main PasteFlow UI for preparing initial context, and an intelligent agent panel with its own mini file browser and @-mention autocomplete for dynamic context refinement during conversations.

## Architecture Overview

### Triple-Layer Architecture: Context + Chat + Terminal
```
┌─────────────────────────────────────────────────────────┐
│                  PasteFlow Main UI                      │
├─────────────────────────────────────────────────────────┤
│  Primary Context Builder:                               │
│  - Full file tree navigation                            │
│  - Bulk file selection with line ranges                 │
│  - System/Role prompts configuration                    │
│  - Pack → Preview → Send to Agent                       │
└─────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┴─────────┐
                    │  Initial Context   │
                    │  Transfer          │
                    └─────────┬─────────┘
                              ▼
┌─────────────────────────────────────────────────────────┐
│                  AI Agent Panel                         │
├─────────────────────────────────────────────────────────┤
│  Dynamic Context Refinement:                            │
│  - Mini file browser (compact file list)                │
│  - @-mention autocomplete in chat input                 │
│  - Real-time file content preview on hover              │
│  - Inline file cards for selected context               │
│  - Token counter with dynamic updates                   │
│  - Chat interface with streaming responses              │
└─────────────────────────────────────────────────────────┘
```

### Component Architecture

```typescript
// High-level component structure with dual context systems
// Search flow: user asks in chat -> agent invokes ripgrep tool -> results streamed back in chat
<AppLayout>
  <MainContentArea>
    {/* Primary context preparation UI */}
    <FileTreeSidebar />
    <ContentPackingArea>
      <Instructions />
      <SelectedFiles />
      <PackButton />
      {/* Send to Agent only appears when content is packed */}
      {isPacked && (
        <SendToAgentButton
          onClick={() => {
            // Opens agent panel and auto-submits to chat
            sendPackedContentToAgent(packedContent);
          }}
        />
      )}
    </ContentPackingArea>
  </MainContentArea>

  <AgentPanel> {/* Intelligent agent workspace */}
    <AgentHeader>
      <ModelSelector />
      <TokenCounter />
      {/* Terminal lives in the bottom panel; no embedded terminal here */}
    </AgentHeader>

    <AgentWorkspace>
      <AgentContextBar>
        <MiniFileList files={workspaceFiles} />
        <SelectedFileCards context={currentContext} />
      </AgentContextBar>

      <ChatMessages />

      <ChatInputWithMention>
        {/* @-mention autocomplete dropdown */}
        <FileAutocompleteDropdown />
      </ChatInputWithMention>
    </AgentWorkspace>
  </AgentPanel>
</AppLayout>
```

## MVP Scope
- file: read, list, info
- search: files (name search, builtin) and code (ripgrep `--line-number --json`)
  - Ripgrep integration implemented in `src/main/tools/ripgrep.ts` with caps and unit tests.
- context: summary (plus compact expand/search utilities)
- Agent panel, @-mention parsing, autocomplete

Out of MVP (move to backlog):
- symbols (AST search)
- analyzeImports / dependency graph

## Implementation Strategy

### Phase 1: Core Chat Infrastructure

#### 1.1 Main Process API Route (`src/main/api-server.ts` - Electron Main Process)
```typescript
import { streamText, convertToModelMessages, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// New chat endpoint using Vercel AI SDK v5
app.post('/api/v1/chat', async (req, res) => {
  const { messages, context } = req.body;

  let retryCount = 0; // populated if using rate-limit wrapper below
  const startedAt = Date.now();
  const result = await streamText({
    model: openai('gpt-5'),
    // Convert UIMessage[] from @ai-sdk/react useChat to model messages
    messages: convertToModelMessages(messages),
    system: 'You are a coding assistant integrated with PasteFlow.',
    maxOutputTokens: 2048,
    temperature: 0.2,
    tools: {
      // Consolidated file operations (Peekaboo-inspired design)
      file: tool({
        description: 'File operations: read, write, move, delete, info, list',
        inputSchema: z.object({
          action: z.enum(['read', 'write', 'move', 'delete', 'info', 'list']),
          path: z.string().optional(),
          paths: z.array(z.string()).optional(), // For batch operations
          content: z.string().optional(),
          destination: z.string().optional(),
          lines: z.object({ start: z.number(), end: z.number() }).optional(),
          recursive: z.boolean().optional(),
          encoding: z.enum(['utf8', 'base64', 'binary']).optional()
        }),
        execute: async (params) => {
          switch (params.action) {
            case 'read':
              if (params.paths) {
                // Batch read multiple files
                return await readMultipleFiles(params.paths, params.encoding);
              }
              return await readFile(params.path, params.lines);

            case 'write':
              return await writeFile(params.path, params.content);

            case 'move':
              return await moveFile(params.path, params.destination);

            case 'delete':
              return await deleteFile(params.path, params.recursive);

            case 'info':
              return await getFileInfo(params.path);

            case 'list':
              return await listDirectory(params.path, params.recursive);
          }
        }
      }),

      // Consolidated search operations (MVP: ripgrep + filename)
      search: tool({
        description: 'Search operations: code (ripgrep), files (name match)',
        inputSchema: z.object({
          action: z.enum(['code', 'files']),
          query: z.string(),
          path: z.string().optional(),
          fileTypes: z.array(z.string()).optional(),
          contextLines: z.number().optional(),
          maxResults: z.number().optional()
        }),
        execute: async (params) => {
          switch (params.action) {
            case 'code':
              // ripgrep JSON output with line numbers
              return await searchCodeWithRipgrep(params.query, {
                path: params.path,
                fileTypes: params.fileTypes,
                contextLines: params.contextLines,
                maxResults: params.maxResults
              });

            case 'files':
              // fast filename substring/glob search (no fuzzy/AST)
              return await searchFilesByName(params.query, params.path);
          }
        }
      }),

      // Consolidated edit operations (Phase 4+)
      edit: tool({
        description: 'Edit operations: diff (unified), block (targeted), multi (batch)',
        inputSchema: z.object({
          action: z.enum(['diff', 'block', 'multi']),
          // diff
          path: z.string().optional(),
          diff: z.string().optional(),
          apply: z.boolean().optional(),
          // block
          search: z.string().optional(),
          replacement: z.string().optional(),
          occurrence: z.number().optional(), // 1-based
          isRegex: z.boolean().optional().default(false),
          preview: z.boolean().optional().default(true),
          // multi
          paths: z.array(z.string()).optional(),
          occurrencePolicy: z.enum(['first', 'all', 'index']).optional(),
          index: z.number().optional(),
          maxFiles: z.number().optional()
        }),
        execute: async (params) => {
          switch (params.action) {
            case 'diff':
              return await applyUnifiedDiffPreviewOrApply(params.path!, params.diff!, params.apply === true);
            case 'block':
              return await editBlockPreviewOrApply({
                path: params.path!,
                search: params.search!,
                replacement: params.replacement ?? '',
                occurrence: params.occurrence,
                isRegex: !!params.isRegex,
                preview: params.preview !== false
              });
            case 'multi':
              return await editMultiPreviewOrApply({
                paths: params.paths!,
                search: params.search!,
                replacement: params.replacement ?? '',
                isRegex: !!params.isRegex,
                occurrencePolicy: params.occurrencePolicy ?? 'first',
                index: params.index,
                preview: params.preview !== false,
                maxFiles: params.maxFiles
              });
          }
        }
      }),

      // Consolidated terminal/process operations (gated by ENABLE_CODE_EXECUTION)
      terminal: tool({
        description: 'Terminal operations: start, interact, output (cursor pagination), list, kill',
        inputSchema: z.object({
          action: z.enum(['start', 'interact', 'output', 'list', 'kill']),
          command: z.string().optional(),
          sessionId: z.string().optional(),
          input: z.string().optional(),
          pid: z.number().optional(),
          args: z.array(z.string()).optional(),
          cwd: z.string().optional(),
          waitForReady: z.boolean().optional(),
          readyPattern: z.string().optional(),
          cursor: z.number().optional(),
          maxBytes: z.number().optional()
        }),
        execute: async (params) => {
          switch (params.action) {
            case 'start':
              return await startProcess({
                command: params.command,
                args: params.args,
                cwd: params.cwd || workspace,
                waitForReady: params.waitForReady,
                readyPattern: params.readyPattern
              });

            case 'interact':
              return await interactWithProcess(params.sessionId, params.input);

            case 'output':
              return await readProcessOutput(params.sessionId, { cursor: params.cursor, maxBytes: params.maxBytes });

            case 'list':
              return await listSessions();

            case 'kill':
              if (params.pid) {
                return await killProcess(params.pid);
              }
              return await forceTerminate(params.sessionId);
          }
        }
      }),

      // Context management (PasteFlow specific)
      context: tool({
        description: 'Context operations: expand, search, summary',
        inputSchema: z.object({
          action: z.enum(['expand', 'search', 'summary']),
          paths: z.array(z.string()).optional(),
          query: z.string().optional()
        }),
        execute: async (params) => {
          switch (params.action) {
            case 'expand':
              // MVP: add explicit files only (no import graph)
              return await expandContext(params.paths);

            case 'search':
              return await searchInContext(params.query);

            case 'summary':
              return await getContextSummary();
          }
        }
      })
    },

    // Stream configuration
    streamOptions: {
      onFinish: async ({ usage, finishReason, response }) => {
        // Expanded observability; assumes startedAt is defined above
        const latencyMs = Date.now() - startedAt;
        const turnId = crypto.randomUUID();

        // Persist per-turn usage summary for fast reporting
        await db.insert('usage_summary', {
          turn_id: turnId,
          session_id: req.headers['x-session-id'],
          model: response?.model,
          prompt_tokens: usage?.promptTokens ?? 0,
          completion_tokens: usage?.completionTokens ?? 0,
          total_tokens: (usage?.promptTokens ?? 0) + (usage?.completionTokens ?? 0),
          tool_count: getAndResetToolCount(turnId),
          tool_time_ms: getAndResetToolTime(turnId),
          latency_ms: latencyMs,
          created_at: Date.now()
        });

        // Optional: store richer diagnostics elsewhere (logs)
        await logUsage({
          responseId: response?.id,
          model: response?.model,
          usage,
          finishReason,
          latencyMs,
          turnId
        });
      }
    }
  });

  // Return as stream
  return result.pipeDataStreamToResponse(res);
});
```

#### Streaming and SSE headers (Express)
If you are not returning a framework-managed `Response` object, set SSE headers explicitly before streaming:

```typescript
// In an Express handler, if you stream manually:
res.setHeader('Content-Type', 'text/event-stream');
res.setHeader('Cache-Control', 'no-cache');
res.setHeader('Connection', 'keep-alive');
// Then pipe the data stream (prefer the SDK helper below):
return result.pipeDataStreamToResponse(res);
```

Note: Using the AI SDK helper (`pipeDataStreamToResponse`) is preferred. It writes the correct headers and streams the SSE body for you.

#### Rate limiting and retries (429)
Implement exponential backoff with jitter and respect `Retry-After` when present. Track retries for observability and show user feedback in the Agent Panel.

```typescript
// Simple helper for retries with 429 handling
async function withRateLimitRetries<T>(fn: () => Promise<T>, opts = { maxRetries: 4, baseDelayMs: 300 }) {
  let attempt = 0;
  while (true) {
    try {
      return { result: await fn(), retryCount: attempt };
    } catch (err: any) {
      const status = err?.status ?? err?.response?.status;
      if (status !== 429 || attempt >= opts.maxRetries) throw err;
      // Respect Retry-After header (seconds)
      const retryAfterSec = Number(err?.response?.headers?.['retry-after']);
      const jitter = Math.floor(Math.random() * opts.baseDelayMs);
      const backoffMs = retryAfterSec
        ? retryAfterSec * 1000
        : Math.min(6000, opts.baseDelayMs * 2 ** attempt) + jitter;
      attempt += 1;
      await new Promise(r => setTimeout(r, backoffMs));
    }
  }
}

// Usage in the chat route (replace the direct streamText call):
const startedAt = Date.now();
const { result, retryCount } = await withRateLimitRetries(() => streamText({
  model: openai('gpt-5'),
  messages: convertToModelMessages(messages),
  system: 'You are a coding assistant integrated with PasteFlow.',
  maxOutputTokens: 2048,
  temperature: 0.2,
  tools,
  streamOptions: {
    onFinish: async ({ usage, finishReason, response }) => {
      const latencyMs = Date.now() - startedAt;
      await logUsage({
        responseId: response?.id,
        model: response?.model,
        usage,
        finishReason,
        latencyMs,
        retryCount
      });
    }
  }
}));

// Provide user feedback in UI (see UI snippet below)
return result.pipeDataStreamToResponse(res);
```

### Tool Payload Shapes (Reference)

Edit (preview-first; apply gated by ENABLE_FILE_WRITE and approval mode)
- Unified diff preview/apply
```
{ "action": "diff", "path": "/abs/file.ts", "diff": "@@ -1,1 +1,1 @@\n-foo\n+bar\n", "apply": false }
```
- Block replacement (targeted)
```
{ "action": "block", "path": "/abs/file.ts", "search": "TODO", "replacement": "", "occurrence": 1, "isRegex": false, "preview": true }
```
- Multi-file batch
```
{ "action": "multi", "paths": ["/abs/a.ts", "/abs/b.ts"], "search": "import abc", "replacement": "import xyz", "occurrencePolicy": "first", "preview": true }
```

Terminal (gated by ENABLE_CODE_EXECUTION)
```
{ "action": "start", "command": "npm", "args": ["test"], "cwd": "/abs/workspace" }
{ "action": "interact", "sessionId": "s1", "input": "q\n" }
{ "action": "output", "sessionId": "s1", "cursor": 0, "maxBytes": 65536 }
{ "action": "kill", "sessionId": "s1" }
```

#### Model selection policy (OpenAI via @ai-sdk/openai)
- Default: openai('gpt-5-mini') — cost-effective and fast for most coding tasks and short edits
- Heavy reasoning or long-form refactors: openai('gpt-5') — best quality and reasoning depth; optionally enable reasoning summaries via providerOptions
- Ultra low-latency or background utilities: openai('gpt-5-nano') — cheapest/fastest, smaller context

Avoid UI-only aliases; prefer documented API model IDs (e.g., 'gpt-5', 'gpt-5-mini').

#### Reasoning continuity outside the SDK (previous_response_id)
When calling OpenAI directly (without the Vercel AI SDK), GPT-5 reasoning models can reuse internal reasoning by passing `previous_response_id`:

```ts
import OpenAI from 'openai';
const client = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

const r1 = await client.responses.create({
  model: 'gpt-5',
  input: 'Analyze the repo structure and summarize key modules.'
});

const r2 = await client.responses.create({
  model: 'gpt-5',
  input: 'Now propose a refactor plan based on the summary.',
  previous_response_id: r1.id
});
```

#### Transport compatibility check (Vercel AI SDK)
- Use `@ai-sdk/react` for `useChat` (v5). Avoid legacy `ai/react`.
- Prefer the built-in `api` fetch transport in `useChat` and return `result.toUIMessageStreamResponse()`/`pipeDataStreamToResponse(res)` on the server.
- Do not reference `DataStreamChatTransport` for v5 unless you intentionally switch to the text protocol; the default UI Message Stream protocol is preferred.

UI feedback pattern for 429:
```tsx
const [notice, setNotice] = useState<string | null>(null);

const chat = useChat({
  api: '/api/v1/chat',
  onError: (err) => {
    if ((err as any)?.status === 429) setNotice('Throttled by the model API. Retrying…');
  },
});

{notice && <Banner type="info">{notice}</Banner>}
```

### Server‑Side Gating & Security
- Paths validated via `validateAndResolvePath` against `getAllowedWorkspacePaths()`.
- Edit apply path requires `ENABLE_FILE_WRITE=true` and approval mode not set to `always` (or explicit approval flow).
- Terminal requires `ENABLE_CODE_EXECUTION=true`; cwd must be within allowed roots; apply rate limiting via `AgentSecurityManager`.

## PR Checklist (Integration)

Use this checklist when integrating or extending the AI SDK agent features.

- Type Safety
  - No `any`; literals preserved with `as const`/`satisfies`.
  - Tool/IPC schemas defined with Zod or jsonSchema; handler types derived via `z.infer`.
  - Discriminated unions for actions/results; exhaustive `switch` with `never`.
  - Branded types applied internally for ids/paths/cursors.
  - Preload bridges and event channels are typed (template-literal channels for terminal output).

- Security & Gating
  - Edit apply path: `ENABLE_FILE_WRITE` and `agent.approvalMode` enforced; errors surfaced to UI.
  - Terminal: `ENABLE_CODE_EXECUTION` required, workspace-limited cwd, risky command approval, rate limiting applied.

- Packaging
  - Terminal: `node-pty` added; `asarUnpack` includes `node_modules/node-pty/**`.

- Testing (per TESTING.md)
  - ≥2 assertions/test; ≤3 mocks/file; no skipped tests; minimal snapshots.
  - Coverage of happy, error, and edge paths; concurrency/rate limiting where applicable.
  - Terminal: pagination, isolation across sessions, invalid param rejections, platform shell selection logic covered.


#### 1.2 Agent Panel with Integrated File Selection

Note on imports: Use `@ai-sdk/react` for `useChat` in v5. Client messages use `parts` (e.g., `{ type: 'text', text: '...' }`). The server must convert via `convertToModelMessages(messages)`.

##### Portability & Backpressure Notes
- Signal handling: CLI examples that trap `SIGINT`/`SIGTERM` are not portable to Windows. Do not mirror those patterns in the renderer.
- Backpressure: Tool callbacks invoked from `streamText` must return fast. For large outputs (search results, terminal logs), return a truncated page and a handle/cursor; let the UI fetch subsequent pages incrementally.

##### Core Agent Component (`src/components/agent-panel.tsx`)
```typescript
import { useChat } from '@ai-sdk/react';
import { useState, useCallback, useRef } from 'react';
import { MiniFileList } from './agent-mini-file-list';
import { ChatInputWithMention } from './agent-chat-input';
import { FileAutocomplete } from './agent-file-autocomplete';

export function AgentPanel() {
  const [dynamicContext, setDynamicContext] = useState<Map<string, FileContext>>(new Map());
  const [showMiniFileList, setShowMiniFileList] = useState(true);

  const { messages, sendMessage, status, append } = useChat({
    // Prefer built-in fetch transport; ensure SDK version supports this.
    api: '/api/v1/chat',

    onBeforeSend: (message) => {
      // Attach dynamic context to message
      return {
        ...message,
        context: Array.from(dynamicContext.values())
      };
    }
  });

  // Listen for incoming packed content from main UI
  useEffect(() => {
    const handlePackedContent = (event: CustomEvent<PackedContent>) => {
      const packed = event.detail;

      // Format and auto-submit the packed content to current chat
      const message = formatPackedContent(packed);

      // This adds to the current thread (or starts a new one if empty)
      append({
        role: 'user',
        content: message,
        metadata: {
          type: 'packed-context',
          files: packed.files.length,
          tokens: packed.metadata.totalTokens
        }
      });

      // Store for reference
      setInitialContext(packed);
    };

    window.addEventListener('pasteflow:send-to-agent', handlePackedContent);
    return () => window.removeEventListener('pasteflow:send-to-agent', handlePackedContent);
  }, [append]);

  const handleFileSelect = useCallback((file: FileData) => {
    setDynamicContext(prev => new Map(prev).set(file.path, {
      path: file.path,
      content: file.content,
      tokenCount: file.tokenCount,
      lines: null
    }));
  }, []);

  const handleFileMention = useCallback(async (path: string, lines?: LineRange) => {
    const fileContent = await loadFileContent(path, lines);
    setDynamicContext(prev => new Map(prev).set(path, fileContent));
  }, []);

  return (
    <div className="agent-panel">
      <AgentHeader>
        <TokenDisplay
          initial={initialContext?.tokenCount || 0}
          dynamic={calculateDynamicTokens(dynamicContext)}
        />
      </AgentHeader>

      <div className="agent-workspace">
        {/* Mini file browser for quick access */}
        <MiniFileList
          files={workspaceFiles}
          selected={Array.from(dynamicContext.keys())}
          onSelect={handleFileSelect}
          collapsed={!showMiniFileList}
        />

        <div className="agent-chat-area">
          {/* Context cards showing selected files */}
          <ContextCards>
            {Array.from(dynamicContext.values()).map(ctx => (
              <FileCard
                key={ctx.path}
                file={ctx}
                onRemove={() => removeDynamicContext(ctx.path)}
                onEditLines={(lines) => updateContextLines(ctx.path, lines)}
              />
            ))}
          </ContextCards>

          {/* Chat messages */}
          <MessageList messages={messages} />

          {/* Smart input with @-mention */}
          <ChatInputWithMention
            onSend={sendMessage}
            onFileMention={handleFileMention}
            disabled={status !== 'ready'}
          />
        </div>
      </div>
    </div>
  );
}
```

##### @-Mention Chat Input (`src/components/agent-chat-input.tsx`)
```typescript
export function ChatInputWithMention({ onSend, onFileMention, disabled }) {
  const [value, setValue] = useState('');
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [autocompleteQuery, setAutocompleteQuery] = useState('');
  const [caretPosition, setCaretPosition] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const caret = e.target.selectionStart;
    setValue(newValue);
    setCaretPosition(caret);

    // Detect @-mention pattern
    const beforeCaret = newValue.slice(0, caret);
    const mentionMatch = beforeCaret.match(/@([^\s]*)$/);

    if (mentionMatch) {
      setAutocompleteQuery(mentionMatch[1]);
      setShowAutocomplete(true);
    } else {
      setShowAutocomplete(false);
    }
  };

  const insertFileMention = useCallback((file: FileData) => {
    const beforeCaret = value.slice(0, caretPosition);
    const afterCaret = value.slice(caretPosition);

    // Find the @ symbol position
    const atPosition = beforeCaret.lastIndexOf('@');
    const beforeAt = value.slice(0, atPosition);

    // Create mention text
    const mentionText = `@${file.path}`;
    const newValue = beforeAt + mentionText + afterCaret;

    setValue(newValue);
    setShowAutocomplete(false);

    // Add file to context
    onFileMention(file.path);

    // Focus back to textarea
    textareaRef.current?.focus();
  }, [value, caretPosition, onFileMention]);

  return (
    <div className="chat-input-container">
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder="Type @ to mention files, ask questions, or request changes..."
        className="chat-input"
        disabled={disabled}
      />

      {showAutocomplete && (
        <FileAutocomplete
          query={autocompleteQuery}
          onSelect={insertFileMention}
          onClose={() => setShowAutocomplete(false)}
          position={getAutocompletePosition(textareaRef.current, caretPosition)}
        />
      )}
    </div>
  );
}
```

##### @-Mention Patterns and Special Commands
```typescript
// Supported @-mention patterns
interface MentionPatterns {
  // File mentions
  '@path/to/file.ts'           // Include entire file
  '@path/to/file.ts:45-120'     // Include specific line range
  '@path/to/file.ts:45'         // Include from line 45 to end

  // Folder mentions (explicit only; no dependency graph)
  '@src/components/'            // Include all files in folder
  '@src/**/*.ts'                // Glob pattern support

  // Special mentions
  '@current'                    // Current file in editor
  '@selected'                   // All files from main UI selection
  '@recent'                     // Recently modified files
  '@related'                    // Files related to current context
  // Removed: '@imports' (out of MVP scope)

  // Context modifiers
  '@clear'                      // Clear all dynamic context
  '@refresh'                    // Refresh file contents
  '@summary'                    // Show context summary
}

// Pattern detection and parsing
function parseMention(text: string): MentionInfo | null {
  const patterns = [
    // File with line range
    /^@([^:]+):(\d+)(?:-(\d+))?$/,
    // Folder pattern
    /^@(.+\/)$/,
    // Glob pattern
    /^@(.+\*+.+)$/,
    // Special command
    /^@(current|selected|recent|related|imports|clear|refresh|summary)$/,
    // Plain file
    /^@(.+)$/
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      return parseMentionMatch(match);
    }
  }
  return null;
}
```

##### File Autocomplete Component (`src/components/agent-file-autocomplete.tsx`)
```typescript
interface FileAutocompleteProps {
  query: string;
  onSelect: (file: FileData) => void;
  onClose: () => void;
  position: { top: number; left: number };
}

export function FileAutocomplete({ query, onSelect, onClose, position }: FileAutocompleteProps) {
  const [filteredFiles, setFilteredFiles] = useState<FileData[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [preview, setPreview] = useState<string | null>(null);

  useEffect(() => {
    // Filter files based on query
    const filtered = searchFiles(query);
    setFilteredFiles(filtered.slice(0, 10)); // Limit to 10 results
  }, [query]);

  const handleKeyDown = (e: KeyboardEvent) => {
    switch(e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, filteredFiles.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
      case 'Tab':
        e.preventDefault();
        if (filteredFiles[selectedIndex]) {
          onSelect(filteredFiles[selectedIndex]);
        }
        break;
      case 'Escape':
        onClose();
        break;
    }
  };

  return (
    <div
      className="file-autocomplete-dropdown"
      style={{ top: position.top, left: position.left }}
    >
      {filteredFiles.map((file, index) => (
        <div
          key={file.path}
          className={`autocomplete-item ${index === selectedIndex ? 'selected' : ''}`}
          onMouseEnter={() => {
            setSelectedIndex(index);
            loadPreview(file.path).then(setPreview);
          }}
          onClick={() => onSelect(file)}
        >
          <FileIcon type={file.type} />
          <div className="file-info">
            <div className="file-path">{highlightMatch(file.path, query)}</div>
            <div className="file-meta">
              {file.size} • {file.tokenCount} tokens
            </div>
          </div>
        </div>
      ))}

      {preview && (
        <div className="file-preview-pane">
          <pre>{preview}</pre>
        </div>
      )}
    </div>
  );
}
```

##### Mini File List Component (`src/components/agent-mini-file-list.tsx`)
```typescript
export function MiniFileList({ files, selected, onSelect, collapsed }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());

  const filteredFiles = useMemo(() => {
    if (!searchQuery) return files;
    return files.filter(f =>
      f.path.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [files, searchQuery]);

  return (
    <div className={`mini-file-list ${collapsed ? 'collapsed' : ''}`}>
      <div className="mini-file-header">
        <input
          type="text"
          placeholder="Search files..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="file-search-input"
        />
      </div>

      <div className="file-tree-mini">
        {renderFileTree(filteredFiles, {
          selected,
          onSelect,
          expandedFolders,
          toggleFolder: (path) => {
            setExpandedFolders(prev => {
              const next = new Set(prev);
              if (next.has(path)) next.delete(path);
              else next.add(path);
              return next;
            });
          }
        })}
      </div>

      <div className="mini-file-footer">
        <span className="file-count">
          {selected.length} files selected • {calculateTokens(selected)} tokens
        </span>
      </div>
    </div>
  );
}
```

### Phase 2: Dual-Context System

#### 2.1 Initial Context Transfer with Auto-Submit
```typescript
// Hook to manage the dual-context system with auto-submission
export function useAgentDualContext() {
  const { fullContent, signature } = usePreviewPack();
  const [initialContext, setInitialContext] = useState<PackedContent | null>(null);
  const [dynamicContext, setDynamicContext] = useState<Map<string, FileContext>>(new Map());

  const transferInitialContext = useCallback((sendMessage: (msg: string) => void) => {
    const packed: PackedContent = {
      files: selectedFiles,
      prompts: { system: selectedSystemPrompts, role: selectedRolePrompts },
      instructions: userInstructions,
      metadata: {
        workspace: selectedFolder,
        totalTokens: tokenEstimate,
        timestamp: Date.now(),
        signature
      }
    };

    // Open agent panel (or focus if already open)
    openAgentPanel();

    // Set the initial context
    setInitialContext(packed);

    // AUTO-SUBMIT: Immediately send packed content as a message
    const contextMessage = formatInitialContext(packed);
    sendMessage(contextMessage);

    // The message appears in the active chat thread (new or existing)
  }, [fullContent, selectedFiles]);

  const formatInitialContext = (packed: PackedContent): string => {
    return `I've prepared the following context for you:

**Files Selected:** ${packed.files.length} files (${packed.metadata.totalTokens} tokens)
${packed.files.map(f => `- ${f.path}${f.lines ? ` (lines ${f.lines.start}-${f.lines.end})` : ''}`).join('\n')}

**Instructions:**
${packed.instructions}

${packed.prompts.system.length > 0 ? `**System Context:**\n${packed.prompts.system.map(p => p.content).join('\n')}` : ''}

Please analyze this code and help me with the instructions above.`;
  };

  const addDynamicFile = useCallback((path: string, content?: string, lines?: LineRange) => {
    setDynamicContext(prev => {
      const next = new Map(prev);
      next.set(path, {
        path,
        content: content || '',
        lines,
        tokenCount: estimateTokens(content || '')
      });
      return next;
    });
  }, []);

  return {
    transferInitialContext,
    addDynamicFile,
    dynamicContext
  };
}
```

#### 2.2 Context-Aware System Prompt
```typescript
function buildSystemPrompt(combinedContext: CombinedContext): string {
  const { initial, dynamic } = combinedContext;

  return `You are an AI coding assistant integrated with PasteFlow.

Current workspace: ${initial?.metadata.workspace || 'Not set'}

Initial Context (from PasteFlow main UI):
- Files: ${initial?.files.length || 0}
- Tokens: ${initial?.metadata.totalTokens || 0}
${initial?.files.map(f => `  - ${f.path}`).join('\n') || 'None'}

Dynamic Context (added via @-mentions):
- Files: ${dynamic.length}
- Tokens: ${calculateDynamicTokens(dynamic)}
${dynamic.map(f => `  - ${f.path}${f.lines ? ` (lines ${f.lines.start}-${f.lines.end})` : ''}`).join('\n')}

Total context: ${combinedContext.totalTokens} tokens

You have access to:
- File operations (read, write, diff)
- Search capabilities within workspace
- Command execution (with approval)
- Dynamic context expansion via user @-mentions

The user can mention additional files using @ in their messages. Pay attention to newly added context.`;
}
```

### Phase 3: Terminal Management Architecture

#### 3.1 Terminal Manager (Electron Main Process)
```typescript
import * as pty from 'node-pty';
import type { IPty } from 'node-pty';

interface TerminalSession {
  id: string;
  name: string;
  pty: IPty;
  output: string[];
  isRunning: boolean;
  currentCommand?: string;
  cwd: string;
}

class TerminalManager {
  private terminals = new Map<string, TerminalSession>();
  private mainWindow: BrowserWindow;

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow;
  }

  create(id: string, options: { name: string; cwd?: string }): TerminalSession {
    // Create pseudo-terminal
    const shell = process.platform === 'win32' ? 'powershell.exe' : 'bash';
    const proc = pty.spawn(shell, [], {
      name: 'xterm-256color',
      cwd: options.cwd || process.cwd(),
      env: process.env as { [key: string]: string },
      cols: 80,
      rows: 30
    });

    const session: TerminalSession = {
      id,
      name: options.name,
      pty: proc,
      output: [],
      isRunning: false,
      cwd: options.cwd || process.cwd()
    };

    // Stream output to renderer
    proc.onData((data: string) => {
      session.output.push(data);

      // Send to renderer for display in xterm.js
      this.mainWindow.webContents.send(`terminal:output:${id}`, data);

      // Also capture for agent context
      this.captureForAgent(id, data);
    });

    proc.onExit(({ exitCode }) => {
      session.isRunning = false;
      this.mainWindow.webContents.send(`terminal:exit:${id}`, { exitCode });
    });

    this.terminals.set(id, session);
    return session;
  }

  getOrCreate(id: string, options: { name: string; cwd?: string }): TerminalSession {
    return this.terminals.get(id) || this.create(id, options);
  }

  // Send command to terminal (visible to user)
  async sendText(id: string, text: string): Promise<void> {
    const session = this.terminals.get(id);
    if (!session) throw new Error(`Terminal ${id} not found`);

    session.currentCommand = text;
    session.isRunning = true;
    session.pty.write(`${text}\n`);
  }

  // Wait for command completion and capture output
  async waitForCompletion(id: string, timeout = 30000): Promise<{
    output: string;
    exitCode: number;
    duration: number;
  }> {
    const session = this.terminals.get(id);
    if (!session) throw new Error(`Terminal ${id} not found`);

    const startTime = Date.now();
    const outputStart = session.output.length;

    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(() => {
        if (!session.isRunning) {
          clearInterval(checkInterval);
          const output = session.output.slice(outputStart).join('');
          resolve({
            output,
            exitCode: 0,
            duration: Date.now() - startTime
          });
        }

        if (Date.now() - startTime > timeout) {
          clearInterval(checkInterval);
          reject(new Error('Command timeout'));
        }
      }, 100);
    });
  }

  // Resize terminal
  resize(id: string, cols: number, rows: number): void {
    const session = this.terminals.get(id);
    if (session) {
      session.pty.resize(cols, rows);
    }
  }

  // Kill terminal
  kill(id: string): void {
    const session = this.terminals.get(id);
    if (session) {
      session.pty.kill();
      this.terminals.delete(id);
    }
  }

  // Capture output for agent context
  private captureForAgent(id: string, data: string): void {
    // Store output for agent to analyze
    // Can implement pattern matching, error detection, etc.
  }
}

// IPC handlers for renderer
ipcMain.handle('terminal:create', async (event, id: string, options) => {
  return terminalManager.create(id, options);
});

ipcMain.handle('terminal:write', async (event, id: string, data: string) => {
  const session = terminalManager.terminals.get(id);
  if (session) {
    session.pty.write(data);
  }
});

ipcMain.handle('terminal:resize', async (event, id: string, cols: number, rows: number) => {
  terminalManager.resize(id, cols, rows);
});
```

#### 3.2 File Operations Tools
```typescript
// Leverage existing PasteFlow file service
const fileTools = {
  readFile: tool({
    execute: async ({ path, lines }) => {
      const validation = validateAndResolvePath(path);
      if (!validation.valid) {
        throw new Error(`Access denied: ${validation.error}`);
      }

      const content = await readTextFile(validation.resolved, lines);
      const tokens = await getMainTokenService().count(content);

      return {
        content,
        tokens,
        path: validation.resolved
      };
    }
  }),

  applyDiff: tool({
    description: 'Apply a diff/patch to a file',
    inputSchema: z.object({
      path: z.string(),
      diff: z.string(), // Unified diff format
      preview: z.boolean().default(true)
    }),
    execute: async ({ path, diff, preview }) => {
      if (preview) {
        // Return preview for user approval
        return {
          type: 'preview',
          original: await readTextFile(path),
          diff,
          modified: applyUnifiedDiff(original, diff)
        };
      }

      // Apply the diff
      const result = await applyDiffToFile(path, diff);
      return { type: 'applied', ...result };
    }
  }),

  // Enhanced file operations (Desktop Commander inspired)
  readMultipleFiles: tool({
    description: 'Read multiple files simultaneously',
    parameters: z.object({
      paths: z.array(z.string()),
      encoding: z.enum(['utf8', 'base64', 'binary']).default('utf8')
    }),
    execute: async ({ paths, encoding }) => {
      const results = await Promise.all(
        paths.map(async path => {
          try {
            const validation = validateAndResolvePath(path);
            if (!validation.valid) {
              return { path, error: validation.error };
            }
            const content = await readTextFile(validation.resolved, { encoding });
            const tokens = await getMainTokenService().count(content);
            return { path, content, tokens, success: true };
          } catch (error) {
            return { path, error: error.message, success: false };
          }
        })
      );
      return results;
    }
  }),

  searchFiles: tool({
    description: 'Find files by name using case-insensitive substring matching',
    parameters: z.object({
      pattern: z.string(),
      directory: z.string().optional(),
      maxDepth: z.number().optional(),
      excludePatterns: z.array(z.string()).optional()
    }),
    execute: async ({ pattern, directory, maxDepth, excludePatterns }) => {
      const searchDir = directory || workspace;
      const files = await findFiles({
        pattern,
        directory: searchDir,
        maxDepth,
        exclude: excludePatterns
      });

      return files.map(f => ({
        path: f.path,
        relativePath: path.relative(workspace, f.path),
        size: f.size,
        modified: f.modified
      }));
    }
  }),

  getFileInfo: tool({
    description: 'Retrieve detailed metadata about a file or directory',
    parameters: z.object({
      path: z.string()
    }),
    execute: async ({ path }) => {
      // Use fs.promises
      const stats = await fs.stat(path);
      const info = {
        path,
        exists: true,
        isDirectory: stats.isDirectory(),
        isFile: stats.isFile(),
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        accessed: stats.atime,
        permissions: stats.mode
      };

      if (stats.isFile()) {
        const content = await readTextFile(path);
        info.lineCount = content.split('\n').length;
        info.tokenCount = await getMainTokenService().count(content);
        info.encoding = detectEncoding(content);
      }

      return info;
    }
  }),

  editBlock: tool({
    description: 'Apply targeted text replacements with character-level diff feedback',
    parameters: z.object({
      path: z.string(),
      searchPattern: z.string(),
      replacement: z.string(),
      occurrence: z.number().optional(), // Which occurrence to replace (1-based)
      preview: z.boolean().default(true)
    }),
    execute: async ({ path, searchPattern, replacement, occurrence, preview }) => {
      const content = await readTextFile(path);

      // Find all occurrences with character positions
      const occurrences = findAllOccurrences(content, searchPattern);

      if (occurrences.length === 0) {
        return { error: 'Pattern not found', pattern: searchPattern };
      }

      if (occurrence && occurrence > occurrences.length) {
        return {
          error: `Only ${occurrences.length} occurrences found`,
          requestedOccurrence: occurrence
        };
      }

      // Character-level diff tracking (Desktop Commander inspired)
      const targetOccurrence = occurrence ? occurrences[occurrence - 1] : occurrences[0];
      const charDiffs = trackCharacterChanges(
        content.substring(targetOccurrence.start, targetOccurrence.end),
        replacement
      );

      if (preview) {
        const previewContent = applyReplacement(
          content,
          targetOccurrence,
          replacement
        );

        return {
          type: 'preview',
          original: content,
          modified: previewContent,
          characterDiffs: charDiffs,
          occurrences: occurrences.length,
          replacedOccurrence: occurrence || 1,
          contextLines: getContextLines(content, targetOccurrence.start, 3)
        };
      }

      // Apply the edit
      const newContent = applyReplacement(content, targetOccurrence, replacement);
      await writeFile(path, newContent);

      return {
        type: 'applied',
        path,
        characterDiffs: charDiffs,
        occurrences: occurrences.length,
        replacedOccurrence: occurrence || 1
      };
    }
  }),

  moveFile: tool({
    description: 'Move or rename files and directories',
    parameters: z.object({
      source: z.string(),
      destination: z.string(),
      overwrite: z.boolean().default(false)
    }),
    execute: async ({ source, destination, overwrite }) => {
      const srcValidation = validateAndResolvePath(source);
      const destValidation = validateAndResolvePath(destination);

      if (!srcValidation.valid || !destValidation.valid) {
        throw new Error('Invalid path');
      }

      if (!overwrite) {
        try {
          await fs.access(destination);
          return { error: 'Destination exists', path: destination };
        } catch {}
      }

      await fs.rename(source, destination);
      return { moved: true, from: source, to: destination };
    }
  }),

  createDirectory: tool({
    description: 'Create a new directory or ensure it exists',
    parameters: z.object({
      path: z.string(),
      recursive: z.boolean().default(true)
    }),
    execute: async ({ path, recursive }) => {
      const validation = validateAndResolvePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.error}`);
      }

      await fs.mkdir(path, { recursive });
      return { created: true, path };
    }
  }),

  listDirectory: tool({
    description: 'Get detailed listing of files and directories',
    parameters: z.object({
      path: z.string(),
      recursive: z.boolean().default(false),
      includeHidden: z.boolean().default(false),
      maxDepth: z.number().optional()
    }),
    execute: async ({ path, recursive, includeHidden, maxDepth }) => {
      const items = await listDir(path, {
        recursive,
        includeHidden,
        maxDepth
      });

      return items.map(item => ({
        name: item.name,
        path: item.path,
        type: item.type,
        size: item.size,
        modified: item.modified,
        permissions: item.permissions
      }));
    }
  })
};
```

#### 3.2 Search and Navigation Tools
```typescript
const searchTools = {
  searchInContext: tool({
    description: 'Search within the packed context',
    parameters: z.object({
      query: z.string(),
      regex: z.boolean().optional()
    }),
    execute: async ({ query, regex }, { context }) => {
      // Search within already-loaded context files
      const results = context.files
        .filter(f => f.content)
        .map(f => ({
          path: f.path,
          matches: findMatches(f.content, query, regex)
        }))
        .filter(r => r.matches.length > 0);

      return results;
    }
  }),

  expandContext: tool({
    description: 'Add more files to the context',
    parameters: z.object({
      paths: z.array(z.string())
    }),
    execute: async ({ paths }) => {
      // Add files to current context (MVP: explicit files only)
      const newFiles = await Promise.all(
        paths.map(p => loadFileWithTokens(p))
      );
      return { added: newFiles, totalTokens: calculateTotal(newFiles) };
    }
  })
};
```

### Phase 4: Advanced Features

#### 4.1 Code Generation with Templates
```typescript
const generationTools = {
  generateFromTemplate: tool({
    description: 'Generate code using templates',
    parameters: z.object({
      template: z.enum(['component', 'hook', 'api-route', 'test']),
      name: z.string(),
      options: z.record(z.any())
    }),
    execute: async ({ template, name, options }) => {
      // Use PasteFlow's conventions
      const conventions = {
        fileNaming: 'kebab-case',
        componentStyle: 'functional',
        testLocation: '__tests__'
      };

      const generated = await generateFromTemplate(
        template,
        name,
        { ...options, conventions }
      );

      return {
        files: generated.files,
        preview: generated.preview,
        tokenCount: await countTokens(generated.content)
      };
    }
  })
};
```

## UI/UX Design

### High-Level Layout (Keep UI Intact, Add Left Agent Panel)
The existing UI remains the same for the Content Area and File Tree. We only add a new Agent Panel docked to the left. The agent terminal is intentionally omitted; the bottom user terminal remains as-is.

```
┌──────────────────────────────────────────────────────────────────────────────┐
│                        Enhanced PasteFlow Layout                             │
├──────────────────────────────┬─────────────────────────────┬────────────────┤
│                              │                             │                │
│  🆕 Agent Panel              │      Content Area           │   File Tree    │
│  [Chat + Context Controls]   │   [Instructions + Files]    │   [Sidebar]    │
│                              │                             │                │
│    ╔═══════════════╗         │   ┌─────────────────┐       │   ┌──────┐     │
│    ║  Model ▾      ║         │   │ Instructions...  │       │   ├──📁 src    │
│    ║  Tokens: 1.2k ║         │   ├─────────────────┤       │   ├──📁 docs   │
│    ║  Mini Files ▾ ║         │   │ Selected Files   │       │   ├──📁 tests  │
│    ║  Chat ▸       ║         │   └─────────────────┘       │   └──📄 README │
│    ╚═══════════════╝         │                             │                │
├──────────────────────────────┴─────────────────────────────┴────────────────┤
│                    💻 Embedded Terminal - User Commands (Collapsible)       │
└──────────────────────────────────────────────────────────────────────────────┘
```

Design constraints and notes:
- Preserve the current Content Area and File Tree as-is.
- Add a left-docked, resizable Agent Panel with chat and context tools.
- No agent xterm in the left panel; bottom terminal remains for user commands.
- “Send to Agent” continues to live in the Content Area and opens/focuses the left panel.

### Dual-Context Agent Panel Layout (Left-Docked)
```typescript
// Three-column layout: Agent Panel (left) | Content Area (center) | File Tree (right)
<div className="app-container with-left-agent">
  <AgentPanel className="left-dock" data-resizable>
    <AgentHeader>
      <ModelSelector />
      <TokenCounter initial={initialTokens} dynamic={dynamicTokens} limit={contextLimit} />
      <ToggleMiniFileList />
      <SettingsButton />
    </AgentHeader>

    <div className="agent-workspace">
      {/* Collapsible mini file browser and selected context cards */}
      <MiniFileList
        files={workspaceFiles}
        selected={dynamicContext}
        onSelect={addToContext}
        onRemove={removeFromContext}
      />

      <MessageList>
        {messages.map(msg => (
          <Message key={msg.id}>
            {msg.role === 'assistant' && msg.toolCalls && (
              <ToolCallVisualization calls={msg.toolCalls} />
            )}
            <MessageContent content={msg.content} />
          </Message>
        ))}
      </MessageList>

      <ChatInputArea>
        <textarea
          ref={inputRef}
          value={message}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder="Type @ to mention files, ask questions, or request changes..."
          className="agent-chat-input"
        />

        {showAutocomplete && (
          <FileAutocompleteDropdown
            query={autocompleteQuery}
            position={getDropdownPosition()}
            files={searchFiles(autocompleteQuery)}
            onSelect={insertFileMention}
            onClose={() => setShowAutocomplete(false)}
          />
        )}

        <InputActions>
          <AttachButton />
          <SendButton disabled={!message.trim()} />
        </InputActions>
      </ChatInputArea>
    </div>
  </AgentPanel>

  {/* Existing central content remains unchanged */}
  <ContentArea className="center-pane">
    <InstructionsInput />
    <SelectedFiles />
    <PackButton />
    <SendToAgentButton enabled={isPacked} />
  </ContentArea>

  {/* Existing file tree sidebar on the right */}
  <FileTreeSidebar className="right-sidebar" />
</div>
```

### AgentPanel Components (UI + Styling)

Use the screenshot attached as the visual reference for the Agent Panel chat spacing, hierarchy, and control placement. The Agent Panel lives as the far‑left column and includes the following UI pieces. Components are functionally separate from the main Content Area but reuse its established visual styles for consistency.

- File Cards Display
  - Purpose: Show currently attached files for the agent as individual rectangular cards inside the panel.
  - Component(s): `AgentAttachmentList` (container) and `AgentFileCard` (item), implemented under `src/components/agent-*`.
  - Styling: Reuse the main content area card styles.
    - Apply shared classes: `file-card`, `file-card-header`, `file-card-name`, `file-card-actions`, `file-card-line-badge` to achieve the same look/feel.
    - Optional variant class: add `agent-file-card` alongside `file-card` to allow minor layout tweaks without diverging from the theme.
  - Behavior: Each card represents a selected file (or file+line range). Provide remove and view actions; reflect pinned state if enabled. Cards are arranged in a simple responsive row/column flow within the Agent Panel.
  - Separation: These cards operate on the Agent’s local attachment stores (`pendingAttachments`, `pinnedAttachments`) and never mutate the main selection used by the Content Area.

- @-Mention Autocomplete Dropdown
  - Purpose: When the user types `@` in the agent chat composer, show a dropdown of matching files to insert as mentions and attach as context.
  - Component(s): `AgentFileAutocomplete` (renderer-only), scoped within the panel.
  - Styling: Reuse the dropdown styles from the main content area.
    - Apply shared classes: `file-autocomplete-dropdown`, `autocomplete-item`, and related `file-info`, `file-meta` utility classes.
    - Scope within the panel container to avoid CSS conflicts; add an `agent-` prefixed wrapper if needed for positioning.
  - Behavior: Positions at the caret; supports ArrowUp/ArrowDown, Enter/Tab to select, Escape to close; updates the Agent attachment store on selection.
  - Separation: Shares visual style only; the filtering, selection, and attachments logic is local to the Agent Panel.

- Header + Controls
  - Include Model selector, token counters (initial/dynamic), and a mini‑files toggle as shown in the screenshot.
  - Add Stop/Cancel during streaming and a Clear Context action.
  - Keep keyboard focus and ARIA labeling aligned with main UI conventions.

- Layout & Placement
  - Left‑docked column as in the ASCII diagram; resizable width with sensible min/max.
  - No embedded agent terminal in Phase 1; the bottom terminal (user commands) remains unchanged.

### Styling Requirements (Shared Visuals, Separate Logic)

- Reuse existing styles from the main Content Area so the Agent Panel feels native:
  - File cards: adopt `.file-card`, `.file-card-header`, `.file-card-name`, `.file-card-actions`, `.file-card-line-badge`.
  - Autocomplete: adopt `.file-autocomplete-dropdown`, `.autocomplete-item`, `.file-info`, `.file-meta`.
- Keep components functionally separate:
  - Implement Agent‑specific components (`AgentAttachmentList`, `AgentFileCard`, `AgentFileAutocomplete`) that import and apply the shared classes but manage their own state and events.
  - Do not import or mutate main selection or Content Area stores; Agent attachments are local to the panel.
- CSS scoping:
  - Wrap panel markup with an `.agent-panel` root. Use an additional `.agent-*` class where necessary for layout overrides while preserving the shared look.
  - Avoid overriding global selectors; prefer adding lightweight variant classes on the Agent components.


### @-Mention Autocomplete Styling
```css
/* Autocomplete dropdown styling */
.file-autocomplete-dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  width: 400px;
  max-height: 300px;
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  z-index: 1000;
}

.autocomplete-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.1s;
}

.autocomplete-item.selected {
  background: var(--accent-blue-dim);
}

.autocomplete-item:hover {
  background: var(--hover-background);
}

.file-info {
  flex: 1;
  margin-left: 8px;
}

.file-path {
  font-size: 14px;
  color: var(--text-primary);
}

.file-meta {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 2px;
}

/* Highlight matching text */
.highlight-match {
  font-weight: 600;
  color: var(--accent-blue);
}

/* File preview on hover */
.file-preview-pane {
  position: absolute;
  left: 100%;
  top: 0;
  width: 400px;
  max-height: 300px;
  margin-left: 8px;
  padding: 12px;
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: auto;
}

.file-preview-pane pre {
  font-size: 12px;
  line-height: 1.4;
  color: var(--text-secondary);
}
```

### Phase 4: Terminal UI Components

#### 4.1 Terminal Panel Component (Renderer)
```typescript
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { useEffect, useRef, useState } from 'react';

interface TerminalPanelProps {
  defaultHeight?: number;
  minHeight?: number;
  maxHeight?: number;
}

export function TerminalPanel({ defaultHeight = 250, minHeight = 100 }: TerminalPanelProps) {
  const [activeTab, setActiveTab] = useState('agent');
  const [tabs, setTabs] = useState<TerminalTab[]>([
    { id: 'agent', name: 'Agent Commands', active: true },
    { id: 'user', name: 'User Terminal', active: false }
  ]);
  const [height, setHeight] = useState(defaultHeight);
  const terminalRefs = useRef<Map<string, Terminal>>(new Map());
  const containerRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  useEffect(() => {
    // Initialize terminals for each tab
    tabs.forEach(tab => {
      if (!terminalRefs.current.has(tab.id)) {
        const terminal = new Terminal({
          theme: {
            background: '#1e1e1e',
            foreground: '#d4d4d4',
            cursor: '#d4d4d4',
            selection: '#264f78',
          },
          fontSize: 14,
          fontFamily: 'Consolas, "Courier New", monospace',
          cursorBlink: true,
        });

        // Add addons
        const fitAddon = new FitAddon();
        terminal.loadAddon(fitAddon);
        terminal.loadAddon(new WebLinksAddon());

        // Store reference
        terminalRefs.current.set(tab.id, terminal);

        // Open terminal in container
        const container = containerRefs.current.get(tab.id);
        if (container) {
          terminal.open(container);
          fitAddon.fit();
        }

        // Handle IPC communication
        terminal.onData((data: string) => {
          // Send user input to main process
          window.electronAPI.terminalWrite(tab.id, data);
        });

        // Listen for output from main process
        window.electronAPI.onTerminalOutput(tab.id, (data: string) => {
          terminal.write(data);
        });
      }
    });

    // Cleanup
    return () => {
      terminalRefs.current.forEach(terminal => terminal.dispose());
      terminalRefs.current.clear();
    };
  }, [tabs]);

  const createNewTab = () => {
    const newTab = {
      id: `terminal-${Date.now()}`,
      name: 'New Terminal',
      active: false
    };
    setTabs([...tabs, newTab]);
    setActiveTab(newTab.id);
  };

  const closeTab = (tabId: string) => {
    // Kill terminal process
    window.electronAPI.terminalKill(tabId);

    // Remove from UI
    setTabs(tabs.filter(t => t.id !== tabId));
    terminalRefs.current.get(tabId)?.dispose();
    terminalRefs.current.delete(tabId);

    // Switch to another tab if needed
    if (activeTab === tabId && tabs.length > 1) {
      setActiveTab(tabs[0].id);
    }
  };

  return (
    <div
      className="terminal-panel"
      style={{ height: `${height}px` }}
    >
      {/* Terminal Tabs */}
      <div className="terminal-tabs">
        {tabs.map(tab => (
          <div
            key={tab.id}
            className={`terminal-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-name">{tab.name}</span>
            {tabs.length > 1 && (
              <button
                className="tab-close"
                onClick={(e) => {
                  e.stopPropagation();
                  closeTab(tab.id);
                }}
              >
                ×
              </button>
            )}
          </div>
        ))}
        <button className="new-tab-btn" onClick={createNewTab}>
          +
        </button>
      </div>

      {/* Terminal Content */}
      <div className="terminal-content">
        {tabs.map(tab => (
          <div
            key={tab.id}
            ref={el => {
              if (el) containerRefs.current.set(tab.id, el);
            }}
            className={`terminal-container ${activeTab === tab.id ? 'active' : 'hidden'}`}
          />
        ))}
      </div>

      {/* Resize Handle */}
      <div
        className="resize-handle"
        onMouseDown={(e) => {
          const startY = e.clientY;
          const startHeight = height;

          const handleMouseMove = (e: MouseEvent) => {
            const deltaY = startY - e.clientY;
            const newHeight = Math.max(minHeight, startHeight + deltaY);
            setHeight(newHeight);
          };

          const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
          };

          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
        }}
      />
    </div>
  );
}

// Styling for command output (no terminal emulator needed)
const commandOutputStyles = `
  .command-output-container {
    background: #1e1e1e;
    border-radius: 6px;
    padding: 12px;
    font-family: 'Consolas', 'Monaco', monospace;
    margin: 8px 0;
  }

  .command-output {
    color: #d4d4d4;
    font-size: 13px;
    line-height: 1.5;
    overflow-x: auto;
  }

  .output-line.stderr {
    color: #f48771;
  }

  .output-line.stdout {
    color: #d4d4d4;
  }
`;
```

#### Tool Call Visualization with Streaming
```typescript
function ToolCallVisualization({ calls }: { calls: ToolCall[] }) {
  return (
    <div className="tool-calls">
      {calls.map(call => (
        <div key={call.id} className="tool-call">
          <div className="tool-header">
            <ToolIcon type={call.toolName} />
            <span>{call.toolName}</span>
            <StatusBadge status={call.status} />
          </div>

          {call.toolName === 'writeFile' && (
            <DiffPreview
              original={call.args.original}
              modified={call.result?.content}
              path={call.args.path}
            />
          )}

          {call.toolName === 'runCommand' && (
            <CommandOutput execution={call.execution} />
          )}
        </div>
      ))}
    </div>
  );
}
```

## Integration Points

### 1. Database Schema Extensions
```typescript
// New tables for agent features
interface AgentTables {
  chat_sessions: {
    id: string;
    workspace_id: string;
    messages: Message[];
    created_at: number;
    updated_at: number;
  };

  tool_executions: {
    id: string;
    session_id: string;
    turn_id: string; // correlates a single assistant turn
    tool_name: string;
    args: any;
    result: any;
    duration_ms: number;
    timestamp: number;
    error?: string;
  };

  // Aggregated, per-turn usage for quick reporting
  usage_summary: {
    turn_id: string;        // unique turn identifier
    session_id: string;
    model: string;
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    tool_count: number;
    tool_time_ms: number;   // sum of durations across tools in the turn
    latency_ms: number;     // end-to-end model latency for the turn
    created_at: number;
  };
}
```

### 2. IPC Bridge Extensions
```typescript
// New IPC channels for agent operations
const agentChannels = {
  'agent:start-session': (context: AgentContext) => Promise<SessionId>,
  'agent:send-message': (message: Message) => Promise<void>,
  'agent:execute-tool': (tool: string, args: any) => Promise<ToolResult>,
  'agent:get-history': (sessionId: string) => Promise<Message[]>
};
```

### 3. Security Considerations
```typescript
class AgentSecurityManager {
  // File access validation
  validateFileAccess(path: string): boolean {
    const validator = getPathValidator(getAllowedWorkspacePaths());
    return validator.validatePath(path).valid;
  }

  // Command execution sandboxing
  validateCommand(command: string): boolean {
    const dangerous = ['rm -rf', 'format', 'del /f'];
    return !dangerous.some(d => command.includes(d));
  }

  // Rate limiting
  checkRateLimit(sessionId: string): boolean {
    const limit = 100; // requests per minute
    return this.requestCount.get(sessionId) < limit;
  }
}
```

### 4. Session Export (UI + CLI)
```typescript
// UI: Agent Panel header button
<Button onClick={() => window.electronAPI.agentExportSession(currentSessionId)}>
  Export Session
</Button>

// IPC bridge (main)
ipcMain.handle('agent:export-session', async (_evt, sessionId: string, outPath?: string) => {
  const session = await db.getSession(sessionId);
  const tools = await db.getToolExecutions(sessionId);
  const usage = await db.getUsageSummaryBySession(sessionId);
  const payload = {
    version: 1,
    sessionId,
    createdAt: session.created_at,
    workspace: session.workspace_id,
    messages: session.messages,
    toolExecutions: tools,
    usageSummary: usage,
  };
  const file = outPath || path.join(app.getPath('downloads'), `pasteflow-session-${sessionId}.json`);
  await fs.writeFile(file, JSON.stringify(payload, null, 2), 'utf8');
  return { file };
});

// CLI (scripts/export-session.ts)
// Usage: pasteflow export-session --id <SESSION_ID> --out ./session.json
```

## Tool and Context Synergy

### How Tools Complement the Dual-Context System

The combination of sophisticated tools with the dual-context architecture creates powerful workflows:

Architecture for search:
- User requests code search via chat (natural language)
- Agent decides to call the `search` tool with action `code`
- The tool executes ripgrep and parses JSON output
- Summarized results are returned via the chat stream, with optional expandable details

#### 1. **Exploratory Search → Precise Context**
```typescript
// Agent uses ripgrep code search, user @-mentions precise files/ranges
Agent: "I'll search for authentication occurrences..."
→ search({ action: 'code', query: 'auth login', path: 'src', fileTypes: ['ts','tsx'], contextLines: 2 })
→ Results: Found matches in 3 files

User: "Great! Let's focus on @src/auth/login-handler.ts:45-120"
→ File automatically added to dynamic context
```

#### 2. **Context-Aware Refactoring**
```typescript
// Multi-file replacements respect both initial and dynamic context
Agent: "I'll update all API endpoints in the selected files"
→ replaceAcrossFiles({
    pattern: "/api/v1/",
    replacement: "/api/v2/",
    files: [...initialContext.files, ...dynamicContext.keys()],
    preview: true
  })
→ User reviews changes before applying
```

#### 3. **Progressive Discovery**
- Start with packed context (main UI selection)
- Agent explores with ripgrep `search.code`
- User refines with @-mentions
- Results are chunked for responsiveness; UI can request more

### Consolidated Tool Usage Examples

#### Simple, Clean Tool Calls
```typescript
// Instead of: readFile(), readMultipleFiles(), getFileInfo(), listDirectory()
// Now just one tool with actions:

// Read a single file
await file({ action: 'read', path: 'src/app.ts' })

// Read multiple files at once
await file({ action: 'read', paths: ['src/app.ts', 'src/config.ts'] })

// Get file info with token count
await file({ action: 'info', path: 'src/app.ts' })

// List directory contents
await file({ action: 'list', path: 'src', recursive: true })
```

#### Unified Search Interface
```typescript
// Unified interface for ripgrep code search + filename search
// Architecture: user asks in chat -> agent decides -> tool executes -> results streamed back

// Exact code search
await search({ action: 'code', query: 'authenticate' })

// Optional: pass fileTypes/path to scope search
await search({ action: 'code', query: 'authenticate', fileTypes: ['ts','tsx'], path: 'src' })

// Find files by name
await search({ action: 'files', query: 'test.ts' })
```

#### Search Tools

We will provide a consolidated `search` tool with two sub-modes:
- `files`: fast filename match using in-memory index (from the file tree)
- `code`: ripgrep-powered code search with line numbers and JSON output (to be implemented from scratch as an agent-only tool; not exposed directly in the UI)

The tool accepts inputs like:

```typescript
const search = tool({
  description: 'Search filenames or code',
  inputSchema: z.object({
    mode: z.enum(['files', 'code']),
    query: z.string().min(1),
    fileGlobs: z.array(z.string()).optional(),
  }),
  async execute({ mode, query, fileGlobs }) {
    // files: use in-memory tree index
    // code: spawn ripgrep with --line-number --json and parse output
  }
});
```

#### Streamlined Editing

### Consolidated Tool Design (Peekaboo-Inspired)

The agent uses just **5 consolidated tools** instead of 30+ individual commands, making tool selection more reliable and the interface cleaner:

#### 1. `file` Tool
Handles all file system operations with an `action` parameter:
- **Actions**: `read`, `write`, `move`, `delete`, `info`, `list`
- **Features**:
  - Batch operations (read multiple files)
  - Line range support for reading
  - Recursive operations for directories
  - Token counting integrated

#### 2. `search` Tool
Unified search interface for discovery:
- **Actions**: `code`, `files`
- **Features**:
  - Ripgrep `--line-number --json` with optional context lines
  - Fast filename substring/glob search

#### 3. `edit` Tool
All text manipulation in one place:
- **Actions**: `replace`, `diff`, `block`, `multi`
- **Features**:
  - Character-level diff tracking
  - Preview mode for all operations
  - Multi-file replacements
  - Occurrence targeting

#### 4. `terminal` Tool
Complete process management:
- **Actions**: `start`, `interact`, `output`, `list`, `kill`
- **Features**:
  - Smart ready detection
  - Interactive command execution
  - Session management
  - Output streaming

#### 5. `context` Tool
PasteFlow-specific context management:
- **Actions**: `expand`, `search`, `summary`
- **Features**:
  - Dynamic context expansion (explicit files only)
  - Token tracking
  - Works with @-mentions

## Advantages Over OpenHands Terminal Approach

### 1. **Dual-Context System**
- **Main UI**: Prepare comprehensive initial context with bulk file selection
- **Agent Panel**: Refine context dynamically during conversation
- **@-Mention**: Natural file inclusion similar to modern chat applications
- **Mini File Browser**: Quick access to workspace files without leaving chat

### 2. **Native Integration**
- No external process management or Python bridge
- Direct access to PasteFlow's file system and security layer
- Seamless context transfer from packed content
- Unified TypeScript codebase

### 3. **Superior User Experience**
- **Intuitive @-mentions**: Type @ to autocomplete file paths
- **Real-time previews**: Hover to see file content before adding
- **Visual diffs**: Preview changes before applying
- **Inline file cards**: See selected context at a glance
- **Token tracking**: Separate counters for initial and dynamic context

### 4. **Enhanced Security**
- All file operations go through existing PathValidator
- No shell access bypass risks
- Granular permission control per tool
- Approval workflows for destructive operations

### 5. **Performance Benefits**
- No IPC overhead for terminal communication
- Shared token counting with main app
- Efficient context reuse via caching
- Parallel tool execution support

### 6. **Developer Experience**
- TypeScript throughout (no Python/OpenHands complexity)
- Single debugging context
- Unified testing approach
- Simpler deployment and maintenance

## Simplified Tool Architecture

### From 30+ Commands to 5 Consolidated Tools

The PasteFlow agent uses a **Peekaboo-inspired design** that consolidates Desktop Commander's comprehensive capabilities into just 5 intuitive tools:

| Tool | Actions | Description | Example |
|------|---------|-------------|---------|
| **`file`** | `read`, `write`, `move`, `delete`, `info`, `list` | All filesystem operations | `file({ action: 'read', path: 'src/app.ts' })` |
| **`search`** | `code`, `files` | Unified search interface (ripgrep + filename) | `search({ action: 'code', query: 'auth' })` |
| **`edit`** | `replace`, `diff`, `block`, `multi` | Text manipulation & edits | `edit({ action: 'multi', paths: [...] })` |
| **`terminal`** | `start`, `interact`, `output`, `list`, `kill` | Process management | `terminal({ action: 'start', command: 'npm' })` |
| **`context`** | `expand`, `search`, `summary` | Context management | `context({ action: 'expand', paths: [...] })` |

### Design Benefits

#### Cognitive Simplicity
- **Before**: LLM had to choose from 30+ tools (decision paralysis)
- **After**: Only 5 tools with clear action parameters (faster, more reliable)

#### Consistent Interface
```typescript
// Every tool follows the same pattern:
tool({
  action: 'specific_action',
  // Context-appropriate parameters
})
```

#### Reduced Token Usage
- Fewer tool descriptions in system prompt
- More concise tool calls
- Clearer intent from LLM

### Comprehensive Capability Coverage

Despite consolidation, all Desktop Commander capabilities remain:

| Desktop Commander Feature | PasteFlow Tool | Action |
|--------------------------|----------------|---------|
| Smart process detection | `terminal` | `start` with `waitForReady` |
| Interactive commands | `terminal` | `interact` |
| Multiple file reads | `file` | `read` with `paths` array |
| Ripgrep code search | `search` | `code` |
| Character-level diffs | `edit` | `block` with diff tracking |
| File metadata | `file` | `info` |
| Process monitoring | `terminal` | `list` |
| Bulk replacements | `edit` | `multi` |

### PasteFlow Unique Additions
- **@-mention autocomplete** for natural file inclusion during chat
- **Dual-context system** with bulk preparation and dynamic refinement
- **Token intelligence** tracking both initial and dynamic context
- **Visual terminal integration** with xterm.js for transparency
- **Progressive pack workflow** for background processing

## Implementation Roadmap

### Phase 1: Core Chat & Tools (Week 1-2)
- [ ] Install Vercel AI SDK v5 deps: ai, @ai-sdk/openai, @ai-sdk/react
- [ ] Implement POST /api/v1/chat with streamText + convertToModelMessages and pipeDataStreamToResponse(res)
- [ ] Stub main-process tools: file (read/info), search (filename fallback), context (summary)
- [ ] Add AgentPanel with @ai-sdk/react useChat and basic @-mention insertion
- [ ] Add SendToAgentButton to dispatch packed content event to AgentPanel

### Phase 2: Dual-Context System (Week 2-3)
- [ ] Implement initial context transfer and system prompt composition
- [ ] Add rate limit retries and usage logging

### Phase 3: Ripgrep Code Search (Week 3-4)
- [ ] Implement ripgrep-based code search as an agent-only tool (not exposed directly in the UI)
- [ ] Command line: `rg --line-number --json --ignore-file <gitignore> <query> <workspace-root>`
- [ ] Parse JSON output to a structured result set (file, line, snippet, matches)
- [ ] Add safe input validation (query length, allowed flags), timeout and cancellation
- [ ] Stream tool results back via chat (summaries + collapsible details)

### Phase 4: Terminal Management (Week 4-5)
- [ ] Set up node-pty in Electron main process
- [ ] Create TerminalManager class and IPC bridge
- [ ] Implement terminal panel UI with xterm.js (tabs optional)
- [ ] Test basic command execution visibility


- [ ] Test basic command execution visibility



### Phase 3: @-Mention & File Selection (Week 3-4)
- [ ] Implement @-mention detection in chat input
- [ ] Build file autocomplete dropdown component
- [ ] Add mini file browser for agent panel
- [ ] Create file card components for selected context
- [ ] Implement real-time file preview on hover
- [ ] Add token counting for both contexts

### Phase 4: Core Agent Tools (Week 4-5)
- [ ] Implement file read/write tools
- [ ] Add search capabilities within context
- [ ] Integrate ripgrep for `search.code` (`--line-number --json`)
- [ ] Implement `search.files` (fast name/glob search)
- [ ] Add multi-file replace with character-level diffs
- [ ] Create diff preview system
- [ ] Build approval workflows for modifications
- [ ] Connect tools to terminal for transparency
- [ ] Add command output capture for agent

### Phase 5: Advanced Features (Week 5-6)
- [ ] Support special @-mention commands
- [ ] Add glob pattern support for folder mentions
- [ ] Implement long-running process management
- [ ] Create terminal output analysis for agent
- [ ] Add session persistence with terminal state
- [ ] Chunk/handle large tool outputs to avoid backpressure

### Phase 6: Polish & Testing (Week 7-8)
- [ ] Optimize terminal performance
- [ ] Refine agent-terminal interaction UX
- [ ] Add keyboard shortcuts
- [ ] Complete test coverage
- [ ] Documentation and examples
- [ ] Security audit for command execution

## Terminal Integration Benefits

### Why Embedded Terminal is Essential

The embedded terminal provides critical benefits for a coding agent:

1. **Trust & Transparency**
   - Users see exactly what commands are running
   - No "black box" operations on their system
   - Builds confidence in agent actions

2. **Developer Workflow**
   - Matches expectations from VS Code, Cursor, Windsurf
   - Supports multiple concurrent processes (dev server + tests)
   - Natural Ctrl+C interruption

3. **Interactive Commands**
   - npm init with prompts
   - git commit interactive editor
   - Interactive debugging sessions

4. **Long-Running Processes**
   - Dev servers remain visible
   - Build watchers show real-time output
   - Test runners with live results

5. **Agent Intelligence**
   - Agent can analyze command output
   - Pattern match for errors
   - React to terminal events

## Dependencies

```json
{
  "dependencies": {
    // AI SDK v5
    "ai": "^5",
    "@ai-sdk/openai": "^2",
    "@ai-sdk/react": "^2",

    // Ripgrep (used via system binary)
    // macOS: brew install ripgrep
    // Windows: choco install ripgrep (or winget)
    // Linux: apt install ripgrep / pacman -S ripgrep

    // Terminal Emulation (Planned Phase 4)
    "xterm": "^5.x",  // Terminal emulator for renderer
    "xterm-addon-fit": "^0.8.x",  // Auto-fit terminal to container
    "xterm-addon-web-links": "^0.9.x",  // Clickable links in terminal
    "node-pty": "^1.x",  // PTY support (main process only)

    // Code Processing
    "diff": "^5.x",  // For diff generation/application
    "unified-diff": "^4.x",  // Diff parsing

    // Already in PasteFlow
    "zod": "^4.x",  // Schema validation
    "express": "^4.x",  // API server
    "tiktoken": "^1.x"  // Token counting
  }
}
```

## Ripgrep Integration Details

Scope: ripgrep will be implemented as a new, agent-only tool (invoked by the agent through chat). It is not exposed directly in the UI.

### `search.code` via ripgrep `--line-number --json`
```typescript
import { spawn } from 'node:child_process';

interface RipgrepOptions {
  path?: string;              // workspace root or subfolder
  fileTypes?: string[];       // e.g., ['ts','tsx']
  contextLines?: number;      // -C
  maxResults?: number;        // cap results to avoid overload
  timeoutMs?: number;         // kill process after timeout
}

export async function searchCodeWithRipgrep(query: string, opts: RipgrepOptions = {}) {
  // Validate inputs (min query length, safe characters, optional glob allow-list)
  const args = [
    '--line-number',
    '--json',
    ...(opts.contextLines ? ['-C', String(opts.contextLines)] : []),
    ...(opts.fileTypes?.flatMap(t => ['-g', `**/*.${t}`]) ?? []),
    query,
    opts.path || '.'
  ];

  const proc = spawn('rg', args, { cwd: opts.path || process.cwd() });
  if (opts.timeoutMs) setTimeout(() => proc.kill('SIGKILL'), opts.timeoutMs);

  const results: any[] = [];
  let count = 0;
  let buffer = '';

  return await new Promise((resolve, reject) => {
    proc.stdout.on('data', (chunk: Buffer) => {
      buffer += chunk.toString('utf8');
      let idx;
      while ((idx = buffer.indexOf('\n')) !== -1) {
        const line = buffer.slice(0, idx); buffer = buffer.slice(idx + 1);
        try {
          const evt = JSON.parse(line);
          if (evt.type === 'match') {
            results.push(evt.data);
            count++;
            if (opts.maxResults && count >= opts.maxResults) proc.kill();
          }
        } catch {/* ignore partials */}
      }
    });
    proc.stderr.on('data', () => {/* optionally log */});
    proc.on('error', reject);
    proc.on('close', () => resolve({ matches: results, truncated: Boolean(opts.maxResults && count >= (opts.maxResults)) }));
  });
}
```

### Backpressure and large results
- Tool callbacks must return fast. Do not stream megabytes through the tool call.
- For big outputs (search results, terminal logs), return an initial page plus a handle:
  - Include `{ id: string, count: number, truncated: boolean }`.
  - Store the full data server-side and expose `search.more` or `terminal.output` pagination.
- UI fetches more on demand to keep the model turn lean.

### Character-Level Diff Tracking
```typescript
// Inspired by Desktop Commander's precise diff tracking
class CharacterDiffTracker {
  trackChanges(original: string, modified: string): CharacterDiff[] {
    const diffs: CharacterDiff[] = [];
    let originalPos = 0;
    let modifiedPos = 0;

    // Use diff-match-patch or similar for character-level tracking
    const patches = diffMatchPatch.diff_main(original, modified);

    for (const [op, text] of patches) {
      if (op === 0) { // Equal
        originalPos += text.length;
        modifiedPos += text.length;
      } else if (op === -1) { // Delete
        diffs.push({
          type: 'delete',
          start: originalPos,
          end: originalPos + text.length,
          text
        });
        originalPos += text.length;
      } else if (op === 1) { // Insert
        diffs.push({
          type: 'insert',
          position: modifiedPos,
          text
        });
        modifiedPos += text.length;
      }
    }

    return diffs;
  }
}
```

## Configuration

## Small Code Corrections

### `node-pty` import & usage
```ts
import * as pty from 'node-pty';
// ...
const shell = process.platform === 'win32' ? 'powershell.exe' : 'bash';
const proc = pty.spawn(shell, [], { name: 'xterm-256color', cwd, env: process.env, cols: 80, rows: 30 });
```

### `fs.promises` everywhere
```ts
import fs from 'node:fs/promises';
// stat/rename/access/mkdir use fs.* (promises)
```

### Ring buffer for terminal output
```ts
class RingBuffer {
  constructor(private max = 1_000_000) {} // bytes
  private buf: Uint8Array = new Uint8Array();
  append(chunk: Uint8Array) {
    const merged = new Uint8Array(this.buf.length + chunk.length);
    merged.set(this.buf);
    merged.set(chunk, this.buf.length);
    if (merged.length > this.max) this.buf = merged.slice(merged.length - this.max);
    else this.buf = merged;
  }
  toString() { return new TextDecoder().decode(this.buf); }
}
```

### `Fuse.js` import
If used elsewhere (non-MVP), ensure default import syntax:
```ts
import Fuse from 'fuse.js';
```

```typescript
// Environment variables
interface AgentConfig {
  OPENAI_API_KEY: string;
  // Or other providers:
  ANTHROPIC_API_KEY?: string;
  AZURE_OPENAI_ENDPOINT?: string;

  // Model selection
  DEFAULT_MODEL: 'gpt-5' | 'claude-3-opus' | 'custom';

  // Limits
  MAX_CONTEXT_TOKENS: number;  // Default: 128000
  MAX_OUTPUT_TOKENS: number;   // Default: 4096
  MAX_TOOLS_PER_TURN: number;  // Default: 10

  // Features
  ENABLE_CODE_EXECUTION: boolean;
  ENABLE_FILE_WRITE: boolean;
  APPROVAL_MODE: 'never' | 'risky' | 'always';
}
```

## Testing Strategy

### Unit Tests
```typescript
// Test individual tools
describe('Agent Tools', () => {
  it('should validate file access before reading', async () => {
    const result = await fileTools.readFile.execute({
      path: '/outside/workspace/file.ts'
    });
    expect(result).toThrow('Access denied');
  });

  it('should apply diffs correctly', async () => {
    const original = 'line1\nline2\nline3';
    const diff = '@@ -2,1 +2,1 @@\n-line2\n+modified line2';
    const result = applyUnifiedDiff(original, diff);
    expect(result).toBe('line1\nmodified line2\nline3');
  });
});
```

### Integration Tests
```typescript
// Test chat flow with mock LLM
describe('Agent Chat Integration', () => {
  it('should transfer packed content to agent context', async () => {
    const packed = await packContent(selectedFiles);
    const session = await startAgentSession(packed);

    expect(session.context.files).toHaveLength(selectedFiles.length);
    expect(session.context.totalTokens).toBeLessThan(MAX_TOKENS);
  });
});
```

## Conclusion

The Vercel AI SDK 5 integration creates a sophisticated **dual-context system** that combines PasteFlow's powerful bulk context preparation with dynamic, conversational context refinement. With fast ripgrep-based code search and character-level diff tracking, this approach reimagines how developers interact with AI coding assistants.

### Key Innovations

#### 1. Dual-Context Workflow

#### Stage 1: Bulk Context Preparation → Auto-Submit
1. User prepares context in PasteFlow main UI:
   - Selects multiple files with line ranges
   - Adds instructions and prompts
   - Clicks **Pack** to prepare content
   - Clicks **Send to Agent** button

2. **Automatic submission** to agent chat:
   - Agent panel opens (or focuses if already open)
   - Packed content is **automatically submitted** as a user message
   - Appears in the current chat thread (new or existing)
   - Agent immediately begins processing the context
   - No manual copy/paste or re-entry needed

#### Stage 2: Dynamic Context Refinement
After the initial context is submitted, users can:
- **@-mention** additional files in follow-up messages
- Use **mini file browser** to add more context
- See **file cards** showing all included files
- Track **token usage** for both initial and dynamic context

### Workflow Example

```
1. User in Main UI:
   - Selects 5 files related to authentication
   - Adds instruction: "Find and fix the login bug"
   - Clicks Pack → Preview shows context
   - Clicks "Send to Agent" ✓

2. Agent Panel Opens:
   - Packed content auto-submitted as first message
   - Chat shows: "I've prepared the following context..."
   - Agent responds: "I can see the issue in auth-handler.ts..."

3. User continues conversation:
   - Types: "Also check @src/utils/validation.ts"
   - The validation file is dynamically added
   - Agent: "Looking at validation.ts, I found..."

4. Context remains active:
   - Initial 5 files stay in context
   - Dynamic @-mentions add to context
   - All visible in file cards
   - Token counter shows both contexts
```

#### 2. Enhanced Tool Ecosystem
- **Ripgrep Search**: Fast, robust code search with context lines
- **Character-Level Diffs**: Precise change tracking for surgical modifications
- **Multi-File Operations**: Bulk replacements with preview and transaction support

### Why This Approach Excels
- **Familiar UX**: @-mentions mirror modern chat applications (Slack, Discord, GitHub)
- **Progressive Disclosure**: Start with prepared context, refine as needed
- **Visual Feedback**: See exactly what files are in context at all times
- **Flexible Workflow**: Support both bulk and exploratory contexts
- **Intelligent Search**: Ripgrep-backed discovery that scales to large repos
- **Transparent Operations**: All agent actions visible in the terminal panel

The implementation leverages:
- PasteFlow's existing file management and security infrastructure
- Vercel AI SDK's production-ready streaming and tool systems
- Desktop Commander-inspired search and diff capabilities
- TypeScript throughout for type safety and maintainability
- Native integration without external process management

This creates an AI agent experience that feels like a natural, intelligent extension of PasteFlow—not just a terminal emulator, but a true coding partner with deep workspace awareness, sophisticated search capabilities, and intuitive interaction patterns. The combination of bulk context preparation, dynamic @-mention refinement, and powerful tools creates a workflow that adapts to how developers actually think and work.
