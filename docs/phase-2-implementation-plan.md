# Phase 2 Implementation Plan — Dual‑Context System and Server‑Composed System Prompt

Executive Summary:
Phase 2 delivers the “Dual‑Context System” for the Agent: a structured initial context hand‑off from the Content Area (with auto‑submit), continued dynamic context via @‑mentions and attachments, and a server‑composed system prompt that reflects both initial and dynamic context. We preserve proven Phase 1 decisions — local composer, agent‑local attachment stores, attachments‑first message assembly, and condensed UI — while adding a versioned, structured context envelope to chat requests for better system prompt composition, observability, and safety.

Important prerequisite: the chat endpoint is not yet present in main. Before Phase 2 can land, complete Phase 1’s `/api/v1/chat` route using Vercel AI SDK v5 streaming.

References:
- Dual‑Context requirements: docs/vercel-ai-sdk-agent-integration.md:824 through docs/vercel-ai-sdk-agent-integration.md:926
- Agent Panel submit and payload ordering: src/components/agent-panel.tsx:118
- LLM payload assembly (attachments before user text): src/components/agent-panel.tsx:151
- useChat hook integration: src/components/agent-panel.tsx:45
- Event bridge “pasteflow:send-to-agent”: src/components/agent-panel.tsx:58
- Agent @‑mention insertion: src/components/agent-chat-input.tsx:106, src/components/agent-file-autocomplete.tsx:25

Current State Summary (from codebase):
- Agent Panel is mounted by default: src/index.tsx
- Agent Panel:
  - Maintains a local composer; does not depend on SDK `setInput`.
  - Manages Pending + Pinned agent‑local attachment stores.
  - Has @‑mention autocomplete for inserting file tokens.
  - Best‑effort loads attachment contents via `allFiles` or `loadFileContent`.
  - Embeds full file contents in fenced blocks and orders them before the user text.
  - Condenses embedded blocks in the UI to “File content: N lines.”
  - Listens for `pasteflow:send-to-agent` and forwards `detail.text` to chat.
- Tests exist for composer typing and @‑mention insertion; Jest mocks `@ai-sdk/react`.

Observed Gaps vs. Master Plan:
- No `/api/v1/chat` server route yet — Phase 2 depends on it.
- No structured `context` in chat requests; currently we embed content only in user text.
- Content Area lacks a “Send to Agent” hand‑off button in the Ready state.
- No server‑side system prompt composition from initial + dynamic context.
- @‑mention tokens don’t capture line ranges; autocomplete lacks up/down orientation logic.
- Jest `useChat` mock lacks `body` and error simulation (e.g., 429) used by Phase 2 tests.

Goals:
1) Initial Context Transfer (auto‑submit) with a standardized, versioned event payload and a summary message in the Agent chat.
2) Dual‑Context transport: add a structured `context` envelope to chat body while preserving Phase 1 embeddings in message text.
3) Server‑Composed System Prompt that summarizes both initial and dynamic context.
4) @‑mention line ranges from the Agent composer and propagation into attachment chips/body payload.
5) Intelligent autocomplete positioning (up/down) with collision padding and arrow alignment.
6) Robust renderer + server tests; extend the `useChat` mock for body + error assertions.

Design Principles:
- Preserve Phase 1 user experience and message determinism by keeping attachments embedded before user text.
- Add a versioned structured envelope for context to support server logic and future iterations.
- Maintain back‑compat for any legacy emitters using `{ text }` only.
- Keep system prompt summary‑only (lists + counts), never re‑embedding full file content.

Dependencies & Prerequisite (Step 0)
- Implement the chat endpoint.
  - Route registration: src/main/api-server.ts (Express).
  - Handler: inline in api-server.ts or a new streaming handler in src/main/api-route-handlers.ts.
  - Use Vercel AI SDK v5: `streamText` and `pipeUIMessageStreamToResponse(res)` for Express.
    - Note: `toUIMessageStreamResponse()` returns a Web Response (e.g., Next.js App Router). For Express, use `pipeUIMessageStreamToResponse(res)`.
  - Path validation and normalization: sanitize any paths in `req.body.context` against `getAllowedWorkspacePaths()`, and list only workspace-relative paths in the system prompt.
  - Respect existing auth middleware and JSON limits (10mb).
- Renderer transport & auth bridging (required for dev/prod parity):
  - Provide an absolute `api` URL for `useChat` (e.g., `http://127.0.0.1:<port>/api/v1/chat`). In dev, a relative `/api` would hit Vite, not the Electron API server.
  - Inject `Authorization: Bearer <token>` header into `useChat` requests (token from main `AuthManager`).
  - Surface `{ apiBase, authToken }` to the renderer via preload/IPC (preferred), or read `~/.pasteflow/server.port` as a fallback.

Detailed Tasks

1) Initial Context Transfer + Auto‑Submit (Renderer)

1.1 SendToAgentButton
- Files:
  - Add src/components/send-to-agent-button.tsx
  - Integrate into src/components/content-area.tsx when `packState.status === 'ready'`, near Packed/Preview/Copy controls.
- Behavior:
  - Enabled when Packed; uses the packed state to build a structured initial context payload.
  - Dispatches `CustomEvent('pasteflow:send-to-agent', { detail })`.
  - Does not mutate selection or tree state.
- Payload: versioned envelope
  - New (structured): `{ context: { version: 1, initial, workspace } }`
    - `initial.files: Array<{ path: string; lines?: { start: number; end: number } | null; tokenCount?: number; bytes?: number; relativePath?: string }>`
    - `initial.prompts: { system: { id: string; name: string; tokenCount?: number }[]; roles?: { id: string; name: string; tokenCount?: number }[]; instructions?: { id: string; name: string; tokenCount?: number }[] }`
    - `initial.user?: { present: boolean; tokenCount: number }`
    - `initial.metadata: { totalTokens: number; signature?: string; timestamp?: number }`
    - `workspace?: string | null`
  - Legacy (supported): `{ text: string }`

1.2 Agent Panel listener and summary append
- src/components/agent-panel.tsx
  - Extend `pasteflow:send-to-agent` listener to detect both payload shapes.
  - If `detail.context` exists: build and append a short summary user message:
    - “Initial context received: N files (~T tokens).”
    - List basenames and line ranges when present; do not embed file contents.
  - Store the latest `initial` context for inclusion in `body.context` on next send.

2) Dual‑Context Transport in Chat Sends (Renderer)

2.0 Transport & Auth bridging (Electron to local API)
- Configure `useChat` with:
  - `api`: absolute URL built from `apiBase` (main provides port) → `http://127.0.0.1:<port>/api/v1/chat`.
  - `headers`: `{ Authorization: 'Bearer ' + authToken }`.
  - `prepareSendMessagesRequest`: attach structured `body.context` while preserving message text embeddings.
- Retrieval of `apiBase` and `authToken`:
  - Preferred: expose via preload or IPC (e.g., `ipcRenderer.invoke('pf:get-api-info')`).
  - Fallback (dev only): read `~/.pasteflow/server.port` and call a small bridge to fetch `AuthManager` token.
  - Alternative (dev only): configure a Vite proxy for `/api/* → http://127.0.0.1:<port>` and still set the Authorization header.

2.1 Include `body.context`
- src/components/agent-panel.tsx
  - Preserve attachments‑first text embedding for LLM.
  - Add `body: { context: { version: 1, dynamic, initial?, workspace? } }` to `sendMessage`.
    - `dynamic.files` sourced from pending+pinned (deduped by path), including optional `lines` and `tokenCount`.
    - Include the last `initial` received (if any) to help the server compose the combined system prompt.
  - Keep UI condensation and the input model unchanged.

2.2 Shared types (use a new file to avoid collision)
- Add src/shared-types/agent-context.ts
  - Define: `AgentAttachmentMeta`, `AgentPackedInitial`, `AgentPackedDynamic`, `AgentContextEnvelope`, `AgentContextBody`.
- Rationale: src/shared-types/messages.ts already exists for worker protocols.
- Optionally re‑export from src/shared-types/index.ts.

3) Server: `/api/v1/chat` and System Prompt Composition

3.1 Phase 1 chat route (prerequisite)
- src/main/api-server.ts: register `POST /api/v1/chat`.
- src/main/api-route-handlers.ts: implement handler using `streamText`.
  - Parse `req.body.messages` (SDK shape) and optional `req.body.context`.
  - Call `streamText({ system, messages, model, tools? })` and pipe with `pipeUIMessageStreamToResponse(res)`.
  - Keep existing auth check and JSON error normalization.

3.2 Build the system prompt from combined context
- Add src/main/agent/system-prompt.ts
  - Export `buildSystemPrompt(combined: CombinedContext): string`.
  - Inputs:
    - `initial?: AgentPackedInitial`
    - `dynamic: AgentPackedDynamic`
    - `workspace?: string | null`
  - Behavior:
    - Summarize counts and file lists with relative paths when available.
    - Include ranges in the listing to indicate partial context.
    - Summarize prompts (system/roles/instructions) and user presence by token counts.
    - Do not include full file content.
    - Tone and outline per docs/vercel-ai-sdk-agent-integration.md:895.
  - Validate and sanitize inputs:
    - Drop any file entries outside `getAllowedWorkspacePaths()`.
    - Convert absolute paths to workspace-relative for listing; redact anything ambiguous.
    - Bound list lengths (e.g., max 50 per section) and include a truncation note when exceeded.

3.3 Wire composition into chat handler
- Derive `combinedContext` from `req.body.context`.
- Compute `system = buildSystemPrompt(combinedContext)`.
- Pass `system` to `streamText` and stream the response to the renderer.

4) @‑Mentions with Line Ranges

4.1 Parse ranges in Agent composer
- src/components/agent-chat-input.tsx
  - Support tokens like `@path/to/file.ts:10-20`.
  - On accept, call `onFileMention(absPath, { start: 10, end: 20 })`.

4.2 Display ranges in chips and propagate to body
- src/components/agent-attachment-list.tsx
  - Add a small `start-end` badge when `lines` present.
- Include ranges in `dynamic.files` for `body.context`.

5) Autocomplete Collision & Orientation

5.1 Orientation logic and classes
- src/components/agent-chat-input.tsx
  - Compute orientation up/down using container bounds and caret anchor.
  - Apply `.autocomplete-dropdown--up` or `.autocomplete-dropdown--down`.
  - Use requestAnimationFrame to avoid layout thrash.
  - Recompute on container scroll/resize; clamp X/Y within collision padding to keep the dropdown visible.

5.2 CSS styling
- src/components/content-area.css
  - Add styling variants for `--up/--down` orientations with collision padding and optional arrow alignment.
  - If a portal is used, measure collisions against the portal container rect.

6) Error Handling
- src/components/agent-panel.tsx
  - Add `onError` in `useChat` options.
  - Show a compact error banner for HTTP 429; preserve Stop behavior during streaming and return to Ready state when stopped.

Testing Plan

Renderer
- agent-transport-auth-config.test.tsx
  - Verify `useChat` receives absolute `api` and `headers.Authorization` set with bearer token from main.
- agent-initial-context-auto-submit.test.tsx
  - Dispatch structured `context` payload; expect a summary user message without content embedding.
  - Verify pending + pinned remain unchanged by initial context.
- agent-context-body-and-order.test.tsx
  - Assert `sendMessage` includes `body.context` with initial+dynamic metadata.
  - Verify message text ordering is attachments-before-user text (src/components/agent-panel.tsx:151).
- agent-mention-line-range.test.tsx
  - Insert `@file.ts:10-20`; assert `onFileMention(absPath, { start: 10, end: 20 })` and chip badge.
- agent-error-banner-429.test.tsx
  - Simulate `onError` with status 429; assert error banner visible and dismissible.
- agent-autocomplete-positioning-up-down.test.tsx
  - Mock caret near bottom; expect `--up` class and computed top above anchor with padding.
  - Mock high anchor; expect `--down` class and maxHeight within visible area.
- agent-autocomplete-collision-padding.test.tsx
  - Narrow viewport; assert left clamped with padding and arrow offset tracks caret x.
  - Panel body scroll; ensure recomputation keeps dropdown visible.

Server
- api-chat-system-prompt.test.ts
  - Mock `streamText` and assert `system` is built from combined initial+dynamic context.
  - Validate sanitization, bounded token sums, and absence of full content in the system string.
  - Assert Express integration uses `pipeUIMessageStreamToResponse(res)` and sends `UI_MESSAGE_STREAM_HEADERS`.

Mocks
- Extend src/__tests__/__mocks__/ai-sdk-react.ts
  - Capture last `body` and `headers` (for context and auth assertions) and allow injecting error codes to exercise `onError` paths (e.g., 429).
  - Provide a hook to simulate network disconnect vs. server error for distinct UI states.

Regression
- All existing suites pass (tree selection, content workflows).
- Adding `SendToAgentButton` does not alter selection or tree expansion state.

Integration Considerations
- Envelope: versioned `{ context: { version: 1, initial?, dynamic, workspace? } }` to support future evolution without breaking clients.
- System prompt: summary only; lists and counts, never full content.
- Transport: remain on `@ai-sdk/react` + server streaming with `pipeUIMessageStreamToResponse(res)` in Express.
- Auth: renderer must send `Authorization: Bearer <token>` and use an absolute API base from main; avoid relying on relative `/api` in dev.
- Performance: compute orientation in rAF; debounce resize/scroll; avoid reflow loops.

Risks & Mitigations
- Token double counting (system + embedded user text)
  - System prompt avoids full content; only counts and lists are included.
- Asynchronous content loads for attachments
  - Refresh attachment content post‑send for subsequent turns as needed.
- Mocking and SDK divergence in tests
  - Extend the `useChat` mock to record bodies and simulate `onError` for deterministic assertions.
- Back‑compat for existing automation
  - Support both `{ text }` legacy and `{ context }` structured event shapes in the listener.

Acceptance Criteria
- `/api/v1/chat` exists and streams via Vercel AI SDK v5.
- “Send to Agent” in Ready state emits a structured event and appends a summary user message.
- Chat requests include `body.context` with initial + dynamic metadata; the server composes a system prompt from combined context.
- Attachments remain embedded before user text; the UI condenses embedded blocks to line‑count summaries.
- @‑mentions accept line ranges and propagate ranges to chips and `body.context`.
- Error banner appears on 429; Stop/Cancel behavior preserved.
- Autocomplete dropdown reorients up/down appropriately and stays within collision padding.
- All tests pass; new tests validate the above behaviors.
- Renderer uses absolute API base and includes Authorization header for chat requests.

Rollout Plan
- Step 0: Add `/api/v1/chat` with streaming (Express + `pipeUIMessageStreamToResponse`).
- Step 0b: Implement renderer transport/auth bridging (absolute `api` + Authorization header via preload/IPC).
- Step 1: Add `SendToAgentButton` and structured event; update Agent Panel listener and summary append.
- Step 2: Add `body.context` in Agent Panel; extend the `useChat` mock and renderer tests.
- Step 3: Implement server system prompt composition and tests.
- Step 4: Add @‑mention line‑range parsing and token counts.
- Step 5: Implement autocomplete collision handling/arrow alignment.
- Step 6: Stabilize and merge after green CI.

Appendix A: Snapshot of Message Assembly (unchanged)
- Final text sent retains attachments‑first ordering (src/components/agent-panel.tsx:151).
- Condensed display via `condenseUserMessageForDisplay()` (src/components/agent-panel.tsx:76).

Appendix B: Proposed Shared Types (src/shared-types/agent-context.ts)
```
// Context sent in chat body (versioned)
export interface AgentAttachmentMeta {
  path: string;
  lines?: { start: number; end: number } | null;
  tokenCount?: number;
  bytes?: number;
  relativePath?: string;
}

export interface AgentPackedInitial {
  files: AgentAttachmentMeta[];
  prompts: {
    system: { id: string; name: string; tokenCount?: number }[];
    roles?: { id: string; name: string; tokenCount?: number }[];
    instructions?: { id: string; name: string; tokenCount?: number }[];
  };
  user?: { present: boolean; tokenCount: number };
  metadata: { totalTokens: number; signature?: string; timestamp?: number };
}

export interface AgentPackedDynamic {
  files: AgentAttachmentMeta[];
}

export interface AgentContextEnvelope {
  version: 1;
  initial?: AgentPackedInitial;
  dynamic: AgentPackedDynamic;
  workspace?: string | null;
}

export interface AgentContextBody {
  context: AgentContextEnvelope;
}
```

Notes on line range semantics
- Dynamic context (Agent mentions/attachments): supports a single optional range per file (`lines?: { start; end } | null`). Multiple ranges can be represented by multiple entries for the same `path`.
- Initial context (from selection/pack): if upstream selection contains multiple ranges for a file, flatten to multiple `files` entries (one per range) to keep `AgentAttachmentMeta.lines` simple and consistent.

Appendix C: Example `buildSystemPrompt` Outline (src/main/agent/system-prompt.ts)
```
import type { AgentContextEnvelope } from "../../shared-types/agent-context";

export interface CombinedContext {
  initial?: AgentContextEnvelope["initial"];
  dynamic: AgentContextEnvelope["dynamic"];
  workspace?: string | null;
}

export function buildSystemPrompt(ctx: CombinedContext): string {
  const ws = ctx.workspace || "Unknown";
  const init = ctx.initial;
  const dyn = ctx.dynamic || { files: [] };

  const initFileCount = init?.files?.length || 0;
  const initTokens = init?.metadata?.totalTokens || 0;
  const dynFileCount = dyn.files.length || 0;

  const lines = [
    `You are an AI coding assistant integrated with PasteFlow.`,
    `\nWorkspace: ${ws}`,
    `\nInitial Context:`,
    `- Files: ${initFileCount}`,
    `- Tokens: ${initTokens}`,
    ...(init?.files?.slice(0, 50).map(f => `  - ${f.relativePath ?? f.path}${f.lines ? ` (lines ${f.lines.start}-${f.lines.end})` : ''}`) || []),
    `\nDynamic Context:`,
    `- Files: ${dynFileCount}`,
    ...(dyn.files.slice(0, 50).map(f => `  - ${f.relativePath ?? f.path}${f.lines ? ` (lines ${f.lines.start}-${f.lines.end})` : ''}`)),
    `\nGuidance:`,
    `- User messages embed full file content before the user text; do not repeat content in the system message.`,
    `- Use the listings as scope indicators and for reasoning about context ranges.`,
  ];
  return lines.join("\n");
}
```

Appendix D: Error Handling Notes
- For 429, display a banner in the Agent Panel; keep Stop responsive and return to Ready when the stream is stopped.
- Persist the banner until the next user action (submit/clear/dismiss).

Appendix E: Transport & Auth Implementation Outline
- Main → Renderer
  - Preload/IPC exposes `{ apiBase, authToken }`.
  - In dev, `apiBase` derives from `~/.pasteflow/server.port` written by main on startup.
- Renderer → `useChat`
  - Configure `api` = `${apiBase}/api/v1/chat`.
  - Configure `headers` = `{ Authorization: 'Bearer ' + authToken }`.
  - Use `prepareSendMessagesRequest` to attach `body.context` while preserving user message embeddings.

Appendix F: Zod Validation Sketch for Context Envelope (Server)
```ts
const LineRange = z.object({ start: z.number().int().min(1), end: z.number().int().min(1) }).refine(v => v.end >= v.start);
const AttachmentMeta = z.object({
  path: z.string().min(1),
  lines: LineRange.nullish(),
  tokenCount: z.number().int().nonnegative().optional(),
  bytes: z.number().int().nonnegative().optional(),
  relativePath: z.string().optional(),
});
const PackedInitial = z.object({
  files: z.array(AttachmentMeta),
  prompts: z.object({
    system: z.array(z.object({ id: z.string(), name: z.string(), tokenCount: z.number().int().nonnegative().optional() })),
    roles: z.array(z.object({ id: z.string(), name: z.string(), tokenCount: z.number().int().nonnegative().optional() })).optional(),
    instructions: z.array(z.object({ id: z.string(), name: z.string(), tokenCount: z.number().int().nonnegative().optional() })).optional(),
  }),
  user: z.object({ present: z.boolean(), tokenCount: z.number().int().nonnegative() }).optional(),
  metadata: z.object({ totalTokens: z.number().int().nonnegative(), signature: z.string().optional(), timestamp: z.number().int().optional() }),
});
const PackedDynamic = z.object({ files: z.array(AttachmentMeta) });
export const ContextEnvelope = z.object({
  version: z.literal(1),
  initial: PackedInitial.optional(),
  dynamic: PackedDynamic,
  workspace: z.string().nullable().optional(),
});
```

Appendix G: Agent Context Types + Zod (src/shared-types/agent-context.ts)
```ts
// src/shared-types/agent-context.ts
import { z } from "zod";

// Types
export interface AgentAttachmentMeta {
  path: string;
  lines?: { start: number; end: number } | null;
  tokenCount?: number;
  bytes?: number;
  relativePath?: string;
}

export interface AgentPackedInitial {
  files: AgentAttachmentMeta[];
  prompts: {
    system: { id: string; name: string; tokenCount?: number }[];
    roles?: { id: string; name: string; tokenCount?: number }[];
    instructions?: { id: string; name: string; tokenCount?: number }[];
  };
  user?: { present: boolean; tokenCount: number };
  metadata: { totalTokens: number; signature?: string; timestamp?: number };
}

export interface AgentPackedDynamic { files: AgentAttachmentMeta[] }

export interface AgentContextEnvelope {
  version: 1;
  initial?: AgentPackedInitial;
  dynamic: AgentPackedDynamic;
  workspace?: string | null;
}

export interface AgentContextBody { context: AgentContextEnvelope }

// Zod Schemas (exported for server validation)
export const AgentLineRangeSchema = z
  .object({ start: z.number().int().min(1), end: z.number().int().min(1) })
  .refine((v) => v.end >= v.start, { message: "end must be >= start" });

export const AgentAttachmentMetaSchema = z.object({
  path: z.string().min(1),
  lines: AgentLineRangeSchema.nullish(),
  tokenCount: z.number().int().nonnegative().optional(),
  bytes: z.number().int().nonnegative().optional(),
  relativePath: z.string().optional(),
});

export const AgentPackedInitialSchema = z.object({
  files: z.array(AgentAttachmentMetaSchema),
  prompts: z.object({
    system: z.array(z.object({ id: z.string(), name: z.string(), tokenCount: z.number().int().nonnegative().optional() })),
    roles: z.array(z.object({ id: z.string(), name: z.string(), tokenCount: z.number().int().nonnegative().optional() })).optional(),
    instructions: z.array(z.object({ id: z.string(), name: z.string(), tokenCount: z.number().int().nonnegative().optional() })).optional(),
  }),
  user: z.object({ present: z.boolean(), tokenCount: z.number().int().nonnegative() }).optional(),
  metadata: z.object({ totalTokens: z.number().int().nonnegative(), signature: z.string().optional(), timestamp: z.number().int().optional() }),
});

export const AgentPackedDynamicSchema = z.object({ files: z.array(AgentAttachmentMetaSchema) });

export const AgentContextEnvelopeSchema = z.object({
  version: z.literal(1),
  initial: AgentPackedInitialSchema.optional(),
  dynamic: AgentPackedDynamicSchema,
  workspace: z.string().nullable().optional(),
});

export const AgentContextBodySchema = z.object({ context: AgentContextEnvelopeSchema });
```

Appendix H: Draft `/api/v1/chat` (Express + streaming)
```ts
// src/main/api-server.ts (registration)
this.app.post('/api/v1/chat', (req, res) => this.routeHandlers.handleChat(req, res));

// src/main/api-route-handlers.ts (handler draft)
import type { Request, Response } from 'express';
import { z } from 'zod';
import { streamText, convertToModelMessages } from 'ai';
import { openai } from '@ai-sdk/openai';
import { AgentContextEnvelopeSchema } from '../../shared-types/agent-context';
import { getAllowedWorkspacePaths } from './workspace-context';
import { toApiError } from './error-normalizer';
import { buildSystemPrompt } from './agent/system-prompt';

// Request body schema (UI messages + optional context)
const ChatBodySchema = z.object({
  messages: z.array(z.any()), // UI messages; converted with convertToModelMessages
  context: AgentContextEnvelopeSchema.optional(),
});

async handleChat(req: Request, res: Response) {
  try {
    const parsed = ChatBodySchema.safeParse(req.body);
    if (!parsed.success) {
      return res.status(400).json(toApiError('VALIDATION_ERROR', 'Invalid body'));
    }

    const uiMessages = parsed.data.messages as any[];
    const modelMessages = convertToModelMessages(uiMessages);

    // Sanitize/normalize context
    const envelope = parsed.data.context;
    const allowed = getAllowedWorkspacePaths();

    // Example sanitization (implement deeply in prod):
    // - drop files outside allowed paths
    // - transform absolute paths to workspace-relative in listings
    const safeEnvelope = envelope ? sanitizeContextEnvelope(envelope, allowed) : undefined;

    const system = buildSystemPrompt({
      initial: safeEnvelope?.initial,
      dynamic: safeEnvelope?.dynamic ?? { files: [] },
      workspace: safeEnvelope?.workspace ?? null,
    });

    const result = streamText({
      model: openai('gpt-4o-mini'),
      system,
      messages: modelMessages,
      // tools: { ... } // optional, future work
    });

    // Stream to Express response with correct headers
    result.pipeUIMessageStreamToResponse(res);
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    return res.status(500).json(toApiError('SERVER_ERROR', message));
  }
}

// helper (sketch)
function sanitizeContextEnvelope(envelope: any, allowed: string[]) {
  // iterate files, ensure item.path starts with one of allowed; if not, drop
  // convert absolute path to relative; keep only filename if ambiguous
  // cap array lengths to reasonable bounds (e.g., 50)
  return envelope;
}

// src/main/agent/system-prompt.ts (stub)
import type { AgentContextEnvelope } from '../../shared-types/agent-context';
export interface CombinedContext {
  initial?: AgentContextEnvelope['initial'];
  dynamic: AgentContextEnvelope['dynamic'];
  workspace?: string | null;
}
export function buildSystemPrompt(ctx: CombinedContext): string {
  // See Appendix C for full outline and rules
  const ws = ctx.workspace || 'Unknown';
  const initCount = ctx.initial?.files?.length || 0;
  const dynCount = ctx.dynamic?.files?.length || 0;
  return [
    'You are an AI coding assistant integrated with PasteFlow.',
    `\nWorkspace: ${ws}`,
    `\nInitial Context: Files=${initCount}`,
    `\nDynamic Context: Files=${dynCount}`,
  ].join('\n');
}
```

Appendix I: AgentPanel `useChat` wiring (headers, api, prepareSendMessagesRequest)
```ts
// src/components/agent-panel.tsx (snippet)
import { useChat } from '@ai-sdk/react';

// Bridge provided by preload/IPC
function useApiInfo() {
  // implementation-specific; returns { apiBase: string, authToken: string }
  return (window as any).__PF_API_INFO || { apiBase: 'http://127.0.0.1:5839', authToken: '' };
}

const { apiBase, authToken } = useApiInfo();

const { messages, sendMessage, status, stop } = useChat({
  api: `${apiBase}/api/v1/chat`,
  headers: { Authorization: `Bearer ${authToken}` },
  // Attach structured envelope without changing user text embeddings
  prepareSendMessagesRequest: ({ id, messages, requestBody }) => {
    const dynamic = buildDynamicFromAttachments(pendingAttachments, pinnedAttachments);
    const envelope = {
      version: 1 as const,
      initial: lastInitialRef.current || undefined,
      dynamic,
      workspace: selectedFolder || null,
    };
    return { ...requestBody, messages, context: envelope };
  },
  onFinish: () => { /* existing clear logic */ },
  onError: (err) => { /* show compact banner for 429, etc. */ }
} as any);

function buildDynamicFromAttachments(pending: Map<string, any>, pinned: Map<string, any>) {
  const all = new Map<string, any>([...pinned, ...pending]);
  const files = Array.from(all.values()).map((v) => ({ path: v.path, lines: v.lines ?? null, tokenCount: v.tokenCount }));
  return { files };
}
```
