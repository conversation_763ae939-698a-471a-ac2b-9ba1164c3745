# Agent Model Management: Design and Implementation Plan

Author: PasteFlow Engineering  
Date: 2025-09-04

## Goals

- Add a user-facing Model Switcher to the Agent Panel to change models at runtime and persist selection.
- Introduce a Model Settings modal for multi-provider configuration (OpenAI, Anthropic, OpenRouter) with secure API key storage and model options (temperature, max tokens).
- Extend the backend to dynamically resolve provider + model for `streamText`, maintain compatibility with the Vercel AI SDK 5 tool-calling setup, and validate availability.
- Keep changes minimally invasive and consistent with existing preferences and security patterns.

## Current State (as implemented)

- Provider: OpenAI only.
- Model resolution: from config default (`DEFAULT_MODEL`), wired via `openai(cfg.DEFAULT_MODEL)` in the chat route.
  - Reference: the `streamText` call uses `openai(cfg.DEFAULT_MODEL)` in the chat handler at [`api-route-handlers.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts#L278-L287) (see line 279).
  - Config default and prefs precedence live in [`config.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/agent/config.ts#L17-L27) with env default set at line 18.
- API key storage: Encrypted in preferences (OpenAI only) using AES‑256‑GCM via `secret-prefs`.
  - References: encryption helpers in [`secret-prefs.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/secret-prefs.ts#L30-L59); current key path `integrations.openai.apiKey` is read in the chat handler at [`api-route-handlers.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts#L263-L277) and set via the renderer Integrations modal at [`integrations-modal.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/integrations-modal.tsx#L72-L127).
- Renderer: Integrations modal supports OpenAI only. No model selector UI.
  - References: [`integrations-modal.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/integrations-modal.tsx#L72-L127), [`agent-panel.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx#L387-L421).
- Streaming + tools: Uses Vercel AI SDK 5 `streamText`, `pipeUIMessageStreamToResponse`, and `getAgentTools` (tool registry).
  - Reference: `streamText` and piping in [`api-route-handlers.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts#L278-L293). Tools registry in [`./agent/tools`](file:///Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts#L27).

## Target Providers and Example Models

Note: model identifiers evolve quickly. We will implement dynamic listing and simple runtime validation, and seed with a conservative static catalog for first run.

- OpenAI
  - Canonical API models (2025-09): `gpt-5`, `gpt-5-mini`, `gpt-5-nano`; non-reasoning ChatGPT router: `gpt-5-chat-latest`.
  - Safe fallback for older accounts: `gpt-4o-mini`.
  - Source: [OpenAI — Introducing GPT‑5](https://openai.com/index/introducing-gpt-5-for-developers/) and [Docs: Models](https://platform.openai.com/docs/models/gpt-5).
- Anthropic
  - Canonical models (2025-09): `claude-sonnet-4-********`, `claude-opus-4-1-********`, `claude-3-5-haiku-********` (and their `*-latest` aliases where provided).
  - Sources: [Anthropic models overview](https://docs.anthropic.com/en/docs/about-claude/models/overview) and [API release notes](https://docs.anthropic.com/en/release-notes/api).
- OpenRouter
  - Gateway to multiple upstreams; use base URL `https://openrouter.ai/api/v1` and API key via `Authorization: Bearer <key>`.
  - Model IDs are namespaced (e.g., `openai/gpt-5`, `openai/gpt-4o-mini`, `anthropic/claude-sonnet-4-********`).
  - Treat as OpenAI-compatible via `createOpenAI({ baseURL, apiKey })`.
  - Source: [OpenRouter quickstart](https://openrouter.ai/docs/quickstart).

We will not hardcode “latest” IDs at runtime; instead:
1) Fetch models list on demand (where feasible) and cache in memory.  
2) Fall back to a seeded static catalog if listing fails.  
3) Validate a chosen model by issuing a short test call.

## UX Additions

### Model Switcher (Agent Panel — under the chat input)

- Location: within the compose area of [`agent-panel.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx#L499-L524), directly under the multiline chat input, aligned left, before the token hint. This mirrors the attached screenshot pattern.
- Implementation notes:
  - Reuse the existing dropdown UI component in [`dropdown.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/dropdown.tsx#L276-L361) with styles from [`dropdown.css`](file:///Users/<USER>/Documents/development/pasteflow/src/components/dropdown.css#L3-L36) and menu styles at [`dropdown.css`](file:///Users/<USER>/Documents/development/pasteflow/src/components/dropdown.css#L78-L101).
  - Show current provider + model; dropdown lists models for the active provider with a search box at the top, capability chips (context size, “tools”), and rough cost tier (low/med/high).
  - Persist selection to preferences: `agent.provider` and `agent.defaultModel` via IPC `/prefs/set`.
  - Emit a subtle toast/banner “Model will apply on the next turn”; do not resend the last message.
  - Gear icon opens Model Settings.

### Model Settings Modal (match existing modal styling)

- Entry points:
  - From the Model Switcher (gear icon adjacent to the dropdown).
  - From the existing integrations/settings entry point.
- Structure:
  - Tabs per provider: OpenAI, Anthropic, OpenRouter.
  - Each tab: API key (password), default model (dropdown), temperature, max output tokens; OpenRouter adds base URL.
- Styling: reuse Radix Dialog conventions used across the app.
  - Wrap with `Dialog.Overlay` class `modal-overlay` and content classes `modal-content workspace-modal`.
  - Header/body/footer follow existing classes (`modal-header`, `modal-body`, `modal-footer`) defined in [`radix-modal.css`](file:///Users/<USER>/Documents/development/pasteflow/src/styles/radix-modal.css#L7-L32) and [`radix-modal.css`](file:///Users/<USER>/Documents/development/pasteflow/src/styles/radix-modal.css#L81-L121).
  - For consistency with the Integrations modal, also mirror spacing and indicator styles from [`integrations-modal.css`](file:///Users/<USER>/Documents/development/pasteflow/src/components/integrations-modal.css#L18-L57).
- Features:
  - Save/Remove keys with encrypted storage via the existing `secret-prefs` mechanism.
  - “Test model” button: calls a backend validation route to perform a minimal prompt and verify auth/availability.
  - Optional: toggle to enable basic cost/usage estimation (placeholder).

## Preferences & Configuration Schema

New/updated preference keys (backed by `DatabaseBridge`):

- `agent.provider`: `openai` | `anthropic` | `openrouter` (default: `openai`).
- `agent.defaultModel`: string (provider-specific model id).
- `agent.temperature`: number (0–2, default 0.2 or conservative default).
- `agent.maxOutputTokens`: number (default from config, e.g., 4000).
- `integrations.openai.apiKey`: secret blob or string (existing).
- `integrations.anthropic.apiKey`: secret blob or string (new).
- `integrations.openrouter.apiKey`: secret blob or string (new).
- `integrations.openrouter.baseUrl`: string (default `https://openrouter.ai/api/v1`).

Config resolution updates ([`config.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/agent/config.ts#L5-L15)):

- Extend `AgentConfig` with:
  - `PROVIDER: 'openai' | 'anthropic' | 'openrouter'`
  - `TEMPERATURE: number`
  - Keep `DEFAULT_MODEL` but clarify it is provider-specific.
  - Keep `MAX_OUTPUT_TOKENS` (already present).
- `resolveAgentConfig()` should read these new preferences (with env fallback) and return a unified config used by the chat route.

## Backend Changes

### Model Resolver

Add `src/main/agent/model-resolver.ts`:

- Inputs: `cfg` (AgentConfig), database (for reading keys), env.
- Resolves API key and constructs the provider client + model function:
  - OpenAI: `import { openai, createOpenAI } from '@ai-sdk/openai'`
    - Use `openai(modelId)` when `process.env.OPENAI_API_KEY` (or decrypted pref) is present.
  - Anthropic: `import { anthropic } from '@ai-sdk/anthropic'`
    - Set `process.env.ANTHROPIC_API_KEY` from decrypted pref.
    - Return `anthropic(modelId)`.
  - OpenRouter: use `createOpenAI({ baseURL, apiKey })` and return `client(modelId)`.
- Returns `{ model, providerName }` for `streamText`.
- On missing keys, throw a typed provider-config error; the chat route maps this to HTTP 503.

### Chat Route Integration

- In the chat handler in [`api-route-handlers.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts#L191-L326):
  - Import and call the Model Resolver.
  - Set provider-specific env vars or use a client with `baseURL` for OpenRouter.
  - Pass `temperature` and `maxOutputTokens` to `streamText` when present.
  - Keep the existing envelope sanitization and tools wiring.
  - Map provider-config errors → 503 with `{ provider, reason }` (current error guard at [`isProviderConfigError`](file:///Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts#L778-L791)).

### New Routes (Validation + Listing)

Add minimal API in `APIRouteHandlers` and register in the server (see router registration in [`api-server.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/api-server.ts#L90-L156)):

- `GET /api/v1/models`
  - Query: `provider?`
  - Returns: `{ provider, models: Array<{ id, label, contextWindowTokens?, costTier?, supportsTools? }> }`
  - Behavior: try to list dynamically (when provider and API key allow); otherwise serve from the static catalog.

- `POST /api/v1/models/validate`
  - Body: `{ provider, model, apiKey?, baseUrl?, temperature?, maxOutputTokens? }`
  - Action: minimal `streamText`/`generateText` with a tiny prompt to verify auth/availability; returns `{ ok: true }` or `{ ok: false, error }`.

### Static Model Catalog (seed)

Add `src/main/agent/models-catalog.ts` exporting a small curated list per provider, e.g.:

- OpenAI: `gpt-5`, `gpt-5-mini`, `gpt-5-nano`, `gpt-4o-mini` (fallback for legacy access).
- Anthropic: `claude-sonnet-4-********`, `claude-3-5-haiku-********` (and `*-latest` aliases where applicable).
- OpenRouter: a few popular upstreams (e.g., `openai/gpt-5`, `openai/gpt-4o-mini`, `anthropic/claude-sonnet-4-********`). Availability varies by account.

Include comments noting that IDs change and the catalog is best-effort.

## Renderer Changes

### Model Switcher Component

- New: `src/components/model-selector.tsx`.
- Props: current provider/model; list of models; callbacks for `onChangeProvider`, `onChangeModel`, `onOpenSettings`.
- Fetch flow:
  - On mount: GET `/api/v1/models?provider=<current>`; fallback to catalog if request fails.
  - Render a searchable dropdown using the shared `Dropdown` component ([`dropdown.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/dropdown.tsx#L276-L361)).
- On selection: update prefs via IPC `/prefs/set` for `agent.provider` and `agent.defaultModel`.
- Show a subtle banner/toast “Model will apply on the next turn”.
- Placement: under the chat input, left-aligned within the form controls row in [`agent-panel.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx#L514-L523). Keep Send button at right.

### Model Settings Modal

- New: `src/components/model-settings-modal.tsx`.
- Tabs: OpenAI, Anthropic, OpenRouter; each with API key input (password), optional base URL (OpenRouter), default model, temperature, max tokens, and “Test model”.
- Persistence: use IPC `/prefs/set` with `encrypted: true` for keys; plain for other settings.
- Validation: call `POST /api/v1/models/validate` and show result inline.
- Styling: match existing modals by reusing the shared classes (`modal-overlay`, `modal-content workspace-modal`, `modal-header`, `modal-body`, `modal-footer`) defined in [`radix-modal.css`](file:///Users/<USER>/Documents/development/pasteflow/src/styles/radix-modal.css#L7-L32) and [`radix-modal.css`](file:///Users/<USER>/Documents/development/pasteflow/src/styles/radix-modal.css#L81-L121). Use spacing patterns from [`integrations-modal.css`](file:///Users/<USER>/Documents/development/pasteflow/src/components/integrations-modal.css#L18-L57).

## Error Handling and UX

- Continue mapping provider config errors to 503 with a provider-specific banner; current 503 banner is shown in the Agent Panel (see [`agent-panel.tsx`](file:///Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx#L423-L444)).
- On invalid model selection (removed/unavailable): show a banner and revert to the last known good model (or to a safe fallback per provider).
- Maintain existing 429 warning UI.

## Security

- Store Anthropic and OpenRouter API keys encrypted using the existing `secret-prefs` module ([`secret-prefs.ts`](file:///Users/<USER>/Documents/development/pasteflow/src/main/secret-prefs.ts#L30-L59)).
- Never echo secrets in logs; only non-sensitive provider and model identifiers.
- For OpenRouter, do not persist custom headers beyond apiKey/baseUrl.

## Testing Strategy

- Unit tests (main):
  - Model Resolver for each provider (with/without keys; base URL for OpenRouter).
  - Chat route chooses the correct model function and passes temperature/max tokens.
  - Validation route returns ok/error for mocked providers.
- Unit tests (renderer):
  - Model selector renders list and persists selection via IPC.
  - Settings modal saves encrypted keys and validates models.
- Integration tests:
  - Simulated end-to-end chat with a mocked provider while swapping providers/models.

## Migration and Defaults

- If `agent.provider` is absent, default to `openai`.
- If `agent.defaultModel` is absent, use existing `DEFAULT_MODEL` from env/prefs; otherwise seed per provider to a safe default:
  - OpenAI: `gpt-4o-mini` (broadest availability) or `gpt-5-mini` when available.
  - Anthropic: `claude-3-5-haiku-********` (or `claude-3-5-haiku-latest`).
  - OpenRouter: `openai/gpt-4o-mini` (or `openai/gpt-5` when available for the account).
- No breaking changes to existing preferences; OpenAI key continues to work as before.

## Step-by-Step Implementation Plan

1) Config foundation
   - Extend `AgentConfig` (provider, temperature). Update `resolveAgentConfig()` to read new prefs.
2) Provider model resolver
   - Add `model-resolver.ts` with OpenAI/Anthropic/OpenRouter adapters.
3) Chat route wiring
   - Use resolver; pass temperature/max tokens to `streamText`. Preserve current tools/streaming behavior.
4) Static catalog + API
   - Add `models-catalog.ts` (seed). Add routes `GET /api/v1/models`, `POST /api/v1/models/validate` and register them in the API server.
5) Renderer UI
   - Add `model-selector.tsx` (compose area dropdown). Add `model-settings-modal.tsx` (tabs, validation, encrypted key storage). Integrate into `agent-panel.tsx` under the chat input.
6) Error UX
   - Improve 503 banner to mention provider; add invalid-model banner and fallback logic.
7) Tests
   - Add unit tests for resolver, API routes, and UI components; keep scope minimal and focused.
8) Docs & release notes
   - Update README and `docs/` with usage, supported providers, and limitations.

## Compatibility with Vercel AI SDK 5

- Continue using `streamText` and tool calling as-is.
- Provider wrappers (`openai`, `anthropic`, `createOpenAI` with baseURL) are compatible with the SDK’s model abstraction. The `model` function returned integrates seamlessly with `streamText`.
- No changes to tool schema or `pipeUIMessageStreamToResponse` are needed.

## Open Questions / Future Enhancements

- Dynamic model listing via provider APIs (rate limits and auth): Start with a seed catalog and add optional live fetch when keys exist.
- Usage/cost estimation: Add per-turn captured token counts + estimators per provider in a future iteration.
- Per-session model overrides: Allow changing model per chat session instead of global default only.
