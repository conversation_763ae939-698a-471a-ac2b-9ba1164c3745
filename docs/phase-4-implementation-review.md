# Phase 4 Implementation Review — Templates, Sessions, Security, IPC

Author: Lead Engineer Review
Date: {auto}

## Executive Summary

Phase 4 is mostly implemented per plan with working code generation templates, durable sessions with export paths (HTTP, IPC, CLI), a central security manager with path validation and per‑session rate limiting, and renderer wiring. Tooling caps/truncation are present and IPC channels were added. Note: legacy renderer feature‑flag injection remains in code and is targeted for removal in this plan.

However, there are several correctness and security issues to address before production:

- Critical: `agent:export-session` IPC handler writes to arbitrary filesystem paths without validation.
- Template bugs: invalid function identifier in API route template; hook test template import path casing mismatch; test file naming deviates from repo convention.
- Session retention/pruning not wired (methods exist but not invoked); messages are not capped.
- Backpressure in chat route (429/caps) not enforced; only tool-level rate limiting is implemented.

With these fixes, Phase 4 will be production‑ready.

## Scope Reviewed

- Agent config (includes legacy feature flag accessors): `src/main/agent/config.ts:1`
- Security manager: `src/main/agent/security-manager.ts:1`
- Tool system + gating: `src/main/agent/tools.ts:1`
- Template engine + templates: `src/main/agent/template-engine.ts:1`, `src/main/agent/templates/*`
- DB schema + bridge: `src/main/db/database-implementation.ts:1`, `src/main/db/database-bridge.ts:1`
- Chat route + instrumentation: `src/main/api-route-handlers.ts:192`
- IPC channels + renderer injection: `src/main/main.ts:324`, `src/main/main.ts:948`
- Renderer feature‑flag usage present (to remove): `src/components/agent-panel.tsx` (reads `window.__PF_FEATURES` to gate UI blocks)
- Legacy/back‑compat branches in tools (to remove): `src/main/agent/tools.ts` (e.g., "Back-compat: read without action", "Back-compat: plain code search without action")
- API server route: `src/main/api-server.ts:155`
- CLI export: `cli/src/commands/sessions.ts:1`, `cli/src/index.ts:14`
- AgentPanel export + session: `src/components/agent-panel.tsx:360`
- Write/apply helper: `src/main/file-service.ts:1`
- Tests: `src/main/__tests__/agent-config-defaults.test.ts:1`, `src/main/__tests__/agent-templates.test.ts:1`, `src/main/__tests__/api-chat-tools.test.ts:1`

## Plan Cross‑Check (Acceptance Criteria)

- Templates tool returns previews for component/hook/api‑route/test
  - Implemented: `generateFromTemplate` tool and library with token counts and preview
  - Issues: API route function name invalid; hook test import path bug; test filename convention
- Session persistence and telemetry
  - Implemented: `chat_sessions`, `tool_executions`, `usage_summary` tables; chat handler upserts session, logs tool execs, inserts usage row
  - Missing: message capping/retention and scheduled pruning
- Export via HTTP, IPC, CLI
  - Implemented: HTTP `POST /api/v1/agent/export-session`, IPC `agent:export-session`, CLI `export-session`
  - Issue: IPC export bypasses path validation; HTTP export restricts to workspace paths (unfriendly)
- Security manager
  - Implemented: path validation, ENABLE_FILE_WRITE + approval mode gating, per‑session tool rate limiting
- IPC channels and zod schemas
  - Implemented: `agent:start-session`, `agent:get-history`, `agent:execute-tool`, `agent:export-session` and schemas
  - Note: IPC schema excludes `generateFromTemplate` from allowed tool names
- Backpressure & truncation
  - Implemented: caps and truncation indicators in tools (search/list; preview clipping)
  - Missing: 429 per‑turn or chat‑level caps in route handler
- UI integration
  - Implemented: AgentPanel starts session, sends `X-Pasteflow-Session`, and includes an export button
- Tests
  - Implemented: config default and template tool unit tests; tool registry test updated
  - Missing: integration tests for session persistence/export; negative/error path tests per plan

## Detailed Findings

### Configuration & Flags
- Correctness: `resolveAgentConfig` merges prefs > env > defaults and supports required keys.
  - Files: `src/main/agent/config.ts:1`

  - Files: `src/main/main.ts:324`
- Minor: `z` import is unused in `config.ts` (lint nit).

### Security Manager
- Path validation enforced via `validateAndResolvePath` for read/write.
  - Files: `src/main/agent/security-manager.ts:1`, `src/main/file-service.ts:1`
- Rate limiting: in‑memory per‑session using `MAX_TOOLS_PER_TURN` cap over 60s window; tools consult `allowToolExecution`.
  - Files: `src/main/agent/security-manager.ts:27`, `src/main/agent/tools.ts:106,153`
- Gating semantics: edit.apply allowed when `ENABLE_FILE_WRITE === true` and approval mode is not `always`. Destructive file actions return structured denials; approval prompts surface when approval mode requires it.
  - Files: `src/main/agent/tools.ts:210,232`

### Tools & Backpressure
- File tool: `read`, `info`, `list` implemented; `write/move/delete` gated with denials; validate paths; list has caps + `truncated`.
  - Files: `src/main/agent/tools.ts:92,116,133,169`
- Search tool: `code` and `files` variants; caps with `truncated`/`clipped` indicators.
  - Files: `src/main/agent/tools.ts:120,141,152,167`
- Edit tool: unified‑diff preview/apply; previews clip; apply gated; path validated; writes via `writeTextFile`.
  - Files: `src/main/agent/tools.ts:176,220,240`
- Template tool: returns preview only with token counts; content clipped to 40k per file.
  - Files: `src/main/agent/tools.ts:258`, `src/main/agent/template-engine.ts:9`

### Templates System
- Component template: PascalCase export, kebab‑case files; optional CSS/test.
  - Files: `src/main/agent/templates/component.ts:1`
- Hook template: ensures `use-` prefix, kebab‑case file.
  - Files: `src/main/agent/templates/hook.ts:3`
- API route template: generates `src/main/routes/<kebab>.ts`.
  - Files: `src/main/agent/templates/api-route.ts:3`
- Test template variants.
  - Files: `src/main/agent/templates/test.ts:3`
- Issues:
  - Invalid identifier: API route uses `export async function <kebab>Handler(...)`, which is not a valid JS identifier when `<kebab>` contains `-`.
    - File: `src/main/agent/templates/api-route.ts:5`
  - Hook test import path uses raw `name` vs kebab‑case; can mismatch generated filename casing.
    - File: `src/main/agent/templates/test.ts:10`
  - Test filename pattern `${kebab}-test.ts[x]` deviates from repo convention `*.test.ts[x]`.
    - Files: `src/main/agent/templates/component.ts:7`, `src/main/agent/templates/test.ts:6`

### Database & Persistence
- Schema: `chat_sessions`, `tool_executions`, `usage_summary` + indexes.
  - Files: `src/main/db/database-implementation.ts:276,240`
- Bridge methods: upsert/get session, insert/list tool execs and usage, prune methods.
  - Files: `src/main/db/database-bridge.ts:240`
- Chat route persists session snapshot and logs tool executions; usage summary rows added.
  - Files: `src/main/api-route-handlers.ts:300,323,333`
- Gaps:
  - Message capping not implemented (stores full `uiMessages` snapshot each turn).
  - Pruning functions exist but are never invoked on startup or timer.

### Chat Route & Backpressure
- Session ID accepted from header `X-Pasteflow-Session`/`x-pf-session-id` or generated.
  - Files: `src/main/api-route-handlers.ts:208`
- Tools wired into stream; DB logging of tool executions attached via `onToolExecute`.
  - Files: `src/main/api-route-handlers.ts:292`
- Backpressure gaps: no per‑turn cap/429; SSE message capping/chunking not present.

### IPC & API & CLI
- IPC channels: `agent:start-session`, `agent:get-history`, `agent:execute-tool`, `agent:export-session` implemented.
  - Files: `src/main/main.ts:324,948`
- IPC schema: Agent schemas defined for start/get/execute/export.
  - Files: `src/main/ipc/schemas.ts:69`
- API export route: `POST /api/v1/agent/export-session` implemented.
  - Files: `src/main/api-server.ts:155`, `src/main/api-route-handlers.ts:580`
- CLI: `pasteflow export-session --id <ID> [--out|--stdout]` implemented; calls HTTP route.
  - Files: `cli/src/commands/sessions.ts:1`, `cli/src/index.ts:14`
- Issues:
  - Security: IPC export writes `outPath` without path validation or workspace checks.
    - File: `src/main/main.ts:999`
  - Usability: API export only writes when `outPath` is provided and requires the path to be inside the active workspace. No default to Downloads.
    - Files: `src/main/api-route-handlers.ts:601`
  - IPC schema `AgentExecuteToolSchema` excludes `generateFromTemplate` tool; IPC cannot invoke it.
    - File: `src/main/ipc/schemas.ts:92`

### UI Integration (AgentPanel)
- Starts session at mount; attaches session header to chat requests; export button wired via IPC.
  - Files: `src/components/agent-panel.tsx:360`
- Error UX: 503 banner for missing API key; basic 429 handler; stop button; tool call UI present.

### Tests
- Added: config defaults, template tool; updated chat/tools wiring test to allow additional tools.
  - Files: `src/main/__tests__/agent-config-defaults.test.ts:1`, `src/main/__tests__/agent-templates.test.ts:1`, `src/main/__tests__/api-chat-tools.test.ts:1`
- Missing per plan: integration tests for chat persistence and export; negative cases (diff failures, IPC invalids, export bad id, search truncation).

## Risks & Security

- IPC export path write (critical): `agent:export-session` writes any `outPath` provided by renderer without validation, allowing writes outside workspace (path traversal/disclosure risk).
  - File: `src/main/main.ts:999`
- API export path restriction (usability): requiring `outPath` be inside workspace is overly strict; expected default to safe location (Downloads) if outPath omitted.
- Session growth: without capping/pruning hooks, `chat_sessions.messages` may grow unbounded.
- Backpressure at chat level: no 429; could allow pathological tool floods despite tool‑level caps.

## Recommendations (Prioritized)

This section expands each recommendation into a concrete, step-by-step implementation plan with examples, files, configuration, testing, estimates, risks, and success criteria.

### 1) Secure IPC Export Path (Critical)

- Implementation Steps:
  - Validate `outPath` via `validateAndResolvePath` when provided; deny with a structured error if outside workspace.
  - If `outPath` is not provided, write to a safe default path under the OS downloads folder, e.g., `${Downloads}/pasteflow-session-<id>.json`.
  - Ensure API route mirrors the same behavior for consistency.
  - Enforce workspace validation strictly.

- Code Examples/Patterns:
  - IPC handler (`src/main/main.ts` — around `agent:export-session`):
    ```ts
    import { app } from 'electron';
    import { validateAndResolvePath } from './file-service';

    ipcMain.handle('agent:export-session', async (_e, sessionId: string, outPath?: string) => {
      // ...fetch payload as today
      const payload = { session: row, toolExecutions: tools, usage };
      // 1) If caller requested a path, enforce workspace validation
      if (typeof outPath === 'string' && outPath.trim().length > 0) {
        const val = validateAndResolvePath(outPath);
        if (!val.ok) return { success: false, error: `PATH_DENIED:${val.reason || val.message}` };
        await fs.promises.writeFile(val.absolutePath, JSON.stringify(payload, null, 2), 'utf8');
        return { success: true, data: { file: val.absolutePath } };
      }
      // 2) Otherwise write to Downloads
      const downloads = app.getPath('downloads');
      const safeName = `pasteflow-session-${sessionId}.json`;
      const filePath = path.join(downloads, safeName);
      await fs.promises.writeFile(filePath, JSON.stringify(payload, null, 2), 'utf8');
      return { success: true, data: { file: filePath } };
    });
    ```
  - API route (`src/main/api-route-handlers.ts` — `handleAgentExportSession`):
    ```ts
    const downloads = app.getPath('downloads');
    const defaultOut = path.join(downloads, `pasteflow-session-${id}.json`);
    // outPath provided -> validate; else write to defaultOut
    ```

- Dependencies/Prerequisites:
  - None beyond existing Electron `app` and current file-service.

- Files/Directories:
  - Modify: `src/main/main.ts:agent:export-session`
  - Modify: `src/main/api-route-handlers.ts:handleAgentExportSession`

- Configuration:
  - Deterministic behavior: workspace‑validated path or Downloads default (no runtime toggles).

- Testing Approach:
  - Unit: stub file-system and assert writes to Downloads when `outPath` omitted; path denial when `outPath` points outside workspace.
  - Integration: exercise HTTP route and IPC path, validate response and file existence.

- Timeline Estimate:
  - 0.5 day (implementation + tests).

- Risk Mitigation:
  - Use try/catch on FS writes and return structured errors.
  - Keep behavior consistent across IPC and HTTP.

- Success Criteria:
  - IPC/HTTP export never writes outside workspace unless defaulting to Downloads; returns `{ file }` path.
  - Negative tests pass (invalid path -> denied; missing session -> 404/NOT_FOUND).

### 2) Fix Template Correctness and Naming

- Implementation Steps:
  - API route template: use a safe identifier derived from `toPascalCase(name)` (e.g., `SampleWidgetHandler`).
  - Hook test template: import by kebab-case filename produced by generator.
  - Test naming: change all generated test filenames to `*.test.ts[x]` (repo standard).

- Code Examples/Patterns:
  - `src/main/agent/templates/api-route.ts`:
    ```ts
    import { toKebabCase, toPascalCase } from './component';
    const kebab = toKebabCase(name);
    const handler = `${toPascalCase(name)}Handler`;
    const content = `import type { Request, Response } from 'express';
export async function ${handler}(req: Request, res: Response) { /* ... */ }
// Registration: app.get('/api/v1/${kebab}', ${handler});`;
    ```
  - `src/main/agent/templates/test.ts` (hook path):
    ```ts
    const kebab = toKebabCase(name.startsWith('use') ? name : 'use-' + name);
    const file = `src/hooks/__tests__/${kebab}.test.ts`;
    const content = `import { ${toPascalCase(name.startsWith('use') ? name : 'use-' + name)} } from "../../hooks/${kebab}";`;
    ```
  - `src/main/agent/templates/component.ts` (test filename):
    ```ts
    const testPath = `src/__tests__/${kebab}.test.tsx`;
    ```

- Dependencies/Prerequisites:
  - None.

- Files/Directories:
  - Modify: `src/main/agent/templates/api-route.ts`
  - Modify: `src/main/agent/templates/test.ts`
  - Modify: `src/main/agent/templates/component.ts`

- Configuration:
  - None.

- Testing Approach:
  - Unit: extend `agent-templates.test.ts` to assert valid identifier (no '-') and correct file paths, including tests’ `.test.ts[x]` suffix.

- Timeline Estimate:
  - 0.25–0.5 day.

- Risk Mitigation:
  - Keep existing exports and file locations stable (only fix names and identifiers).

- Success Criteria:
  - Generated artifacts compile by default and tests resolve correct paths.

### 3) Add Session Retention and Pruning Wiring

- Implementation Steps:
  - Cap messages stored per session to last N (e.g., 50) and/or byte cap before `upsertChatSession`.
  - Add scheduled pruning for telemetry tables using the existing DB methods on app start and weekly.
- Apply fixed caps in code or reuse existing config values.

- Code Examples/Patterns:
  - Chat route (`handleChat`):
    ```ts
    const maxMsgs = Number(process.env.PF_AGENT_MAX_SESSION_MESSAGES ?? 50);
    const msgJson = JSON.stringify(uiMessages.slice(-maxMsgs));
    await this.db.upsertChatSession(sessionId, msgJson, ws ? String(ws.id) : null);
    ```
  - Main process schedule (`src/main/main.ts` after DB init):
    ```ts
    const retentionDays = Number(process.env.PF_AGENT_TELEMETRY_RETENTION_DAYS ?? 90);
    const cutoff = () => Date.now() - retentionDays * 24 * 60 * 60 * 1000;
    const prune = async () => {
      try { await database.pruneToolExecutions(cutoff()); await database.pruneUsageSummary(cutoff()); } catch {}
    };
    // On start and weekly
    void prune();
    setInterval(prune, 7 * 24 * 60 * 60 * 1000);
    ```

- Dependencies/Prerequisites:
  - None (methods already exist).

- Files/Directories:
  - Modify: `src/main/api-route-handlers.ts:handleChat`
  - Modify: `src/main/main.ts` (startup schedule)

- Configuration:
  - `PF_AGENT_MAX_SESSION_MESSAGES` (default: 50)
  - `PF_AGENT_TELEMETRY_RETENTION_DAYS` (default: 90)

- Testing Approach:
  - Unit: assert cap in `handleChat` (slice and length); mock `upsertChatSession`.
  - Integration: call chat twice and verify only the last N messages are persisted.

- Timeline Estimate:
  - 0.5 day.

- Risk Mitigation:
  - Keep caps conservative; document behavior.

- Success Criteria:
  - DB contains capped message arrays; prune reduces old rows; no errors on schedule.

### 4) Backpressure at Chat Route (429)

- Implementation Steps:
  - Add a quick rate‑limit guard in `handleChat` that checks per‑session allowance before starting a new turn.
  - Optionally add a new method to `AgentSecurityManager` like `isRateLimited(sessionId)` to probe without incrementing.

- Code Examples/Patterns:
  - `AgentSecurityManager`:
    ```ts
    isRateLimited(sessionId: string): boolean {
      const now = Date.now();
      const ent = this.toolUsageBySession.get(sessionId);
      const windowMs = 60_000;
      const cap = Math.max(1, Math.min(1000, this.cfg.MAX_TOOLS_PER_TURN));
      if (!ent || now - ent.windowStart > windowMs) return false;
      return ent.count >= cap;
    }
    ```
  - `handleChat` guard:
    ```ts
    if (security.isRateLimited(sessionId)) {
      return res.status(429).json(toApiError('RATE_LIMITED', 'Too many tool calls this minute'));
    }
    ```

- Dependencies/Prerequisites:
  - None.

- Files/Directories:
  - Modify: `src/main/agent/security-manager.ts`
  - Modify: `src/main/api-route-handlers.ts:handleChat`

- Configuration:
  - Uses existing `MAX_TOOLS_PER_TURN` as a rate cap.

- Testing Approach:
  - Unit: simulate usage map state and assert 429 returned by `handleChat`.

- Timeline Estimate:
  - 0.25–0.5 day.

- Risk Mitigation:
  - Only return 429 when already saturated; keep tool‑level limiter as the primary guard.

- Success Criteria:
  - Subsequent chat requests can return 429 under load; tool‑level limiter still works.

### 5) IPC Schemas and Parity

- Implementation Steps:
  - Extend `AgentExecuteToolSchema` enum to include `generateFromTemplate`.
  - In `agent:execute-tool`, pass `onToolExecute` and log to DB for parity with HTTP chat route.

- Code Examples/Patterns:
  - Schema (`src/main/ipc/schemas.ts`):
    ```ts
    tool: z.enum(['file', 'search', 'edit', 'context', 'terminal', 'generateFromTemplate'])
    ```
  - IPC handler:
    ```ts
    const tools = getAgentTools({ security, config: cfg, sessionId: parsed.data.sessionId, onToolExecute: async (n,a,r,m) => {
      try { await database.insertToolExecution({ sessionId: parsed.data.sessionId, toolName: String(n), args: a, result: r, status: 'ok', startedAt: (m as any)?.startedAt ?? null, durationMs: (m as any)?.durationMs ?? null }); } catch {}
    }});
    ```

- Dependencies/Prerequisites:
  - None.

- Files/Directories:
  - Modify: `src/main/ipc/schemas.ts`
  - Modify: `src/main/main.ts:agent:execute-tool`

- Configuration:
  - None.

- Testing Approach:
  - Unit: schema accepts template tool; handler returns result; DB log row inserted.

- Timeline Estimate:
  - 0.25 day.

- Risk Mitigation:
  - Keep enum in sync with `getAgentTools()`; consider deriving allowed keys dynamically in a follow-up.

- Success Criteria:
  - IPC can run `generateFromTemplate`; DB telemetry collected for IPC tool runs.

### 6) API Export UX

- Implementation Steps:
  - Mirror IPC default-to-Downloads behavior when `outPath` is not provided.
  - When `outPath` is provided: validate via `validateAndResolvePath`; deny if outside workspace.
  - Preserve `download === true` behavior to return JSON directly without writing.

- Code Examples/Patterns:
  - `handleAgentExportSession`:
    ```ts
    if (parsed.data.download === true) return res.json(ok(payload));
    if (parsed.data.outPath) { /* validate like IPC and write */ }
    else { const file = path.join(app.getPath('downloads'), `pasteflow-session-${id}.json`); await fs.promises.writeFile(file, json, 'utf8'); return res.json(ok({ file })); }
    ```

- Dependencies/Prerequisites:
  - Electron `app` (import via `nodeRequire('electron')` if ESM boundary requires).

- Files/Directories:
  - Modify: `src/main/api-route-handlers.ts:handleAgentExportSession`

- Configuration:
  - Same deterministic behavior as IPC export.

- Testing Approach:
  - Unit/Integration: assert default write to Downloads; `download=true` returns JSON; outside-workspace `outPath` denied.

- Timeline Estimate:
  - 0.25 day.

- Risk Mitigation:
  - Return JSON when `download=true`; handle filesystem write errors with structured responses.

- Success Criteria:
  - Route writes to a predictable file path by default; CLI shows `{ file }` when `--out` omitted.

### 7) Cleanups

- Implementation Steps:
  - Remove unused `z` import in `src/main/agent/config.ts`.

- Code Examples/Patterns:
  - `config.ts`: delete `import { z } from 'zod';` if unused.

- Dependencies/Prerequisites:
  - None.

- Files/Directories:
  - Modify: `src/main/agent/config.ts`
  - Optional modify: `src/components/agent-panel.tsx`

- Configuration:
  - None.

- Testing Approach:
  - Lint: no unused imports; UI compiles.

- Timeline Estimate:
  - 0.1 day.

- Risk Mitigation:
  - None; low-risk cleanup.

- Success Criteria:
  - Lint passes; no unused imports remain.

### 8) Remove Feature Flags and Legacy/Back‑Compat Code

- Implementation Steps:
  - Remove renderer feature flag injection and usage:
    - Delete `toRendererFeatureFlags` usage in `src/main/main.ts` (the `executeJavaScript` call writing `window.__PF_FEATURES`).
    - Remove reads of `window.__PF_FEATURES` in `src/components/agent-panel.tsx`; always render tool calls UI without gating.
  - Remove legacy/back‑compat branches in tools:
    - In `src/main/agent/tools.ts`, require explicit `action` for file and search tools; remove shapes without `action` (comments labeled “Back‑compat”).
    - Adjust Zod schemas accordingly and update the `execute` logic to assume explicit actions only.
  - Remove unused flag accessors from config:
    - In `src/main/agent/config.ts`, remove the renderer‑facing feature flag exporter and any dead code related to UI flags.
  - Update docs and tests to reflect the strict, non‑flagged behavior.

- Code Examples/Patterns:
  - Tools Zod schemas (file/search):
    ```ts
    // Before: union includes shapes without action for back-compat
    // After: require explicit action for every call
    const fileParams = z.union([
      z.object({ action: z.literal('read'), path: z.string(), lines: lineRange.optional() }),
      z.object({ action: z.literal('info'), path: z.string() }),
      z.object({ action: z.literal('list'), directory: z.string(), recursive: z.boolean().optional(), maxResults: z.number().int().min(1).max(10_000).optional() }),
      z.object({ action: z.literal('write'), path: z.string(), content: z.string(), apply: z.boolean().optional().default(true) }),
      z.object({ action: z.literal('move'), from: z.string(), to: z.string() }),
      z.object({ action: z.literal('delete'), path: z.string() }),
    ]);

    const searchParams = z.union([
      z.object({ action: z.literal('code'), query: z.string().min(1).max(256), directory: z.string().optional(), maxResults: z.number().int().min(1).max(50_000).optional() }),
      z.object({ action: z.literal('files'), pattern: z.string().min(1).max(256), directory: z.string().optional(), maxResults: z.number().int().min(1).max(10_000).optional(), recursive: z.boolean().optional() }),
    ]);
    ```
  - AgentPanel: remove `const ff = (window as any).__PF_FEATURES || {}` and any gating conditional.

- Dependencies/Prerequisites:
  - None.

- Files/Directories:
  - Modify: `src/main/main.ts` (remove `__PF_FEATURES` injection)
  - Modify: `src/components/agent-panel.tsx` (remove `__PF_FEATURES` reads and UI gating)
  - Modify: `src/main/agent/tools.ts` (remove back‑compat branches and shapes)
  - Modify: `src/main/agent/config.ts` (remove renderer feature flag exporter)
  - Update tests under `src/main/__tests__` that reference back‑compat shapes or UI gating

- Configuration:
  - None; no runtime toggles.

- Testing Approach:
  - Unit: tools reject payloads missing `action`; AgentPanel renders tool call UI unconditionally; build succeeds without references to `__PF_FEATURES`.
  - Negative: calls that omit `action` fail validation with clear errors.

- Timeline Estimate:
  - 0.5–1 day (code + tests + docs updates).

- Risk Mitigation:
  - Communicate breaking changes; update any internal scripts relying on implicit shapes.

- Success Criteria:
  - No references to `__PF_FEATURES` or renderer flags; tools only accept explicit `action` payloads; tests updated and passing.

## Production Readiness

- Status: Not ready for production until the IPC export path validation is fixed and the template bugs are addressed. The other items (session capping/pruning and backpressure at chat route) are strongly recommended for robustness and should be implemented now.

## Appendix — Notable Code References

 - Renderer feature‑flag injection (to remove): `src/main/main.ts:324`
- Session logging in chat route: `src/main/api-route-handlers.ts:292`
- IPC export (requires validation): `src/main/main.ts:999`
- HTTP export handler: `src/main/api-route-handlers.ts:580`
- Template engine: `src/main/agent/template-engine.ts:9`
- API route template (identifier bug): `src/main/agent/templates/api-route.ts:5`
- Hook test template (import casing bug): `src/main/agent/templates/test.ts:10`
- Tool caps/truncation: `src/main/agent/tools.ts:120,141,167,176`
