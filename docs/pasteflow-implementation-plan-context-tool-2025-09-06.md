# Implementation Plan — Extend Consolidated Context Tool (`summary` | `expand` | `search`) — 2025-09-06

## Scope Summary
- Extend the `context` consolidated tool to support:
  - `summary`: current behavior (counts files in envelope)
  - `expand`: load file contents or line ranges with token counts and byte caps
  - `search`: query workspace using existing ripgrep utility and return compact matches
- Maintain compatibility and keep the standalone `search` tool unchanged.

## Current Code Analysis
- Tools registry: `src/main/agent/tools.ts`
  - `context` tool (summary only) at `:288-321`.
  - Utilities available in scope: `validateAndResolvePath`, `readTextFile`, `getMainTokenService`.
  - Existing tools: `file`, `search`, `edit`, `context`, `terminal` (stub), `generateFromTemplate`.
- Ripgrep utility: `src/main/tools/ripgrep.ts` (`runRipgrepJson(...)`) — accepts query, directory, maxResults, etc.; returns JSON with `files`, `totalMatches`, `truncated`.
- Security/path validation: `validateAndResolvePath` and workspace roots via `getAllowedWorkspacePaths` (indirectly used by `file-service` and ripgrep runner).
- Tool execution logging: registry passes `onToolExecute` meta with duration to DB at `src/main/api-route-handlers.ts:306-321`.
- Tests exist for file/search/edit tools: `src/main/__tests__/agent-tools-file-and-edit.test.ts`, `ripgrep-runner.test.ts`.

## Design Overview
- Replace the `context` tool schema with an explicit JSON schema supporting three actions.
- Implement each action with consistent error returns (`{ type: 'error', code, message }`) and logging via `onToolExecute`.
- Keep I/O safe: validate paths, cap sizes, detect likely binary files, and limit counts.
- Do not remove the standalone `search` tool; the `context.search` is a convenience where the agent has a context envelope already.

## Step-by-Step Implementation
1) Update `context` tool schema and handler
- File: `src/main/agent/tools.ts`
- Replace current `context` tool block with an action-based schema:
  - Input schema (using `jsonSchema`):
    - `{ action: 'summary' | 'expand' | 'search', envelope?: any, files?: Array<{ path: string; lines?: { start: number; end: number } }>, maxBytes?: number, query?: string, directory?: string, maxResults?: number }`
  - Behavior:
    - `summary` (default if `action` omitted): count initial/dynamic files; preserves existing output shape `{ initialFiles, dynamicFiles }`.
    - `expand`: for each `files[i]`:
      - Validate with `validateAndResolvePath`; read with `readTextFile`; if `isLikelyBinary` true -> return error for that file or skip with marker.
      - If `lines` specified, slice accordingly (1-based, inclusive); cap output to `maxBytes` (default 50_000).
      - Compute token counts via `tokenService.countTokens(content)`.
      - Return array of `{ path: absolutePath, content, bytes, tokenCount, truncated }`.
    - `search`: delegate to `runRipgrepJson` with `query`, `directory`, `maxResults`. Map to compact structure: `{ files: [{ path, matches: [{ line, text }] }], totalMatches, truncated }`.
  - Ensure each action path calls `onToolExecute('context', params, result, { startedAt, durationMs })`.
  - Return structured errors for invalid input (`VALIDATION_ERROR`) and path denials (`PATH_DENIED`).

2) Parameter validation and limits
- Use conservative caps: `maxFiles` (e.g., 20), `maxBytes` per file (~50 KB), and ignore extra entries to protect model context.
- For `expand`, if both `lines` and full-file requested, prefer `lines` and mark when truncated.
- Normalize file paths via `validateAndResolvePath` and abort any out-of-root access.

3) Tests
- File: `src/main/__tests__/agent-tools-context.test.ts` (new)
  - `summary` returns `{ initialFiles, dynamicFiles }` for a mock envelope.
  - `expand` returns content for a small temp file; respects `lines` slices and `maxBytes` caps.
  - `search` returns matches for a seeded test directory using the ripgrep runner (mock or sample fixture).
  - Verify `onToolExecute` meta duration is forwarded.

## Function/Method Signatures
- Modified tool declaration (within `getAgentTools`):
  - `const context = (tool as any)({ description: 'Context utilities', inputSchema: jsonSchema({...}), execute: async (params: any) => { /* per action */ } });`
- No external/public API signature changes.

## Database Schema Changes
- None.

## API Endpoint Changes
- None; tool registry is passed to `streamText` automatically and remains internal.

## Configuration Changes
- None required. Optional caps can be derived from existing `AgentConfig` (e.g., `MAX_RESULTS_PER_TOOL`); keep constants in code or read from config if desired.

## Dependencies
- No new packages; reuses `runRipgrepJson`, `validateAndResolvePath`, `readTextFile`, `getMainTokenService`.

## Error Handling & Edge Cases
- Treat missing/invalid `files` or `query` parameters as `VALIDATION_ERROR` with message.
- Binary files: either return per-item error marker `{ type: 'error', code: 'BINARY_FILE' }` or skip with `skipped: true` flag.
- Line ranges outside file length: clamp to valid range; if empty after clamp, return empty content.
- Respect workspace security boundaries via `validateAndResolvePath`.

## Backward Compatibility & Migration Strategy
- `summary` mode remains the default when `action` is omitted; existing agent prompts remain valid.
- Standalone `search` tool remains available; `context.search` adds a convenience path.

## Risk Assessment & Regression Prevention
- Affected areas: agent tool execution logging and schema handling.
- Low risk; changes are localized to the `context` tool.
- New tests as above. Ensure existing tool tests continue to pass.

## Implementation Order
1. Implement schema/action handler in `tools.ts` with conservative caps.
2. Add tests and adjust fixtures if needed.
3. Verify tool execution logs are captured with duration meta.

## Shared Components / Dependencies with Other Improvements
- None at the API level. Shares `runRipgrepJson` with the standalone `search` tool.
