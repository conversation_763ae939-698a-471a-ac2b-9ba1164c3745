# Phase 3 Implementation Plan — Agent Tools and Ripgrep Code Search

Executive Summary:
Phase 3 adds server-executed tools to the Agent with a focus on a production‑ready ripgrep code search tool and a consolidated tool surface that fits the project’s dual‑context architecture. We integrate tools into the existing `/api/v1/chat` streaming route (Vercel AI SDK v5), maintain strict workspace/path validation, and provide lightweight UI surfacing for tool calls inside the Agent Panel. Terminal execution is stubbed for approval flows and kept minimal to unblock Phase 4’s Terminal Manager UI and node‑pty integration.

Important: one Phase 2 prerequisite remains partially unshipped — the renderer auth/API bridge. AgentPanel falls back to `http://127.0.0.1:5839` without an Authorization header if preload does not expose `{ apiBase, authToken }` as `window.__PF_API_INFO`. Ship this bridge first (see “0) Preflight & Dependencies”).

Deliverables:
- Consolidated tool set (server‑side) registered on the chat route:
  - file: read/info (safe within workspace)
  - search: ripgrep‑based code search (`--json --line-number`) with timeouts and safe inputs
  - edit: diff preview + apply (approval‑gated)
  - context: generate summary from the dual‑context envelope
  - terminal: placeholder stub (returns not‑implemented; approval gate + API scaffold)
- Tool execution telemetry and structured results streamed back via Vercel AI SDK
- Renderer UI: compact tool‑call visualization attached to assistant turns
- Tests: unit tests for ripgrep JSON parsing and path validation guards; server handler tests for tool wiring; renderer tests for tool call rendering

Alignment With Integration Plan:
- Main reference: docs/vercel-ai-sdk-agent-integration.md (Phase 3: Ripgrep Code Search; consolidated tools pattern; tool summaries; server streaming)
 - Phases 1–2 status: Largely complete (default-on Agent Panel, structured context envelope, server‑composed system prompt, absolute API wiring). Remaining gap: renderer auth bridging — expose `{ apiBase, authToken }` via preload/IPC so `/api/v1/chat` works end‑to‑end under AuthManager.
- Phase 4 preview: Terminal Manager (node‑pty), richer terminal UI (xterm.js), and approval flows build on this phase’s tool scaffolding


## Architecture & Technical Requirements

0) Preflight & Dependencies (must do before tools)
- Renderer auth/API bridge: in preload/IPC, expose `{ apiBase, authToken }` as `window.__PF_API_INFO` (or equivalent hook) so `AgentPanel` sends `Authorization: Bearer <token>` to `/api/v1/chat`.
- Ensure server writes `~/.pasteflow/server.port` (already implemented in `src/main/main.ts`) and that preload reads AuthManager’s token for dev/prod parity.
- Confirm `OPENAI_API_KEY` is configured and that the `/api/v1/chat` route streams successfully with a minimal prompt before enabling tools.

- Server (Express) — `src/main/api-route-handlers.ts` `handleChat`
  - Use Vercel AI SDK v5 `streamText` with `tools` registry and `convertToModelMessages` for history
  - Compose system prompt via `buildSystemPrompt` (already added in Phase 2) and pass consolidated tools
  - Perform path validation using existing `validateAndResolvePath` and `getAllowedWorkspacePaths()`
  - Enforce input limits: query length, max results, timeouts, bytes caps
  - Add tool execution metrics (count + duration) for observability (optional Phase 3b)

- Tools Registry (new) — `src/main/agent/tools.ts`
  - Exports `getAgentTools(deps): Tools` (inlined in `handleChat` or separate)
  - Consolidated tools with zod input schemas; return structured JSON objects
  - Tools must never read or write outside the allowed workspace
  - Note: do not import any write helpers from `file-service` in Phase 3; the repo currently exposes `validateAndResolvePath`, `statFile`, and `readTextFile` only. “Apply” lands in Phase 4 (see below).

- Ripgrep Integration (new) — `src/main/tools/ripgrep.ts`
  - Execute `rg` via `child_process.spawn` with `--json --line-number` and root directory from `getAllowedWorkspacePaths()` (or provided safe subdir)
  - Honor `.gitignore` (existing ignore utils are available) or pass `--ignore-file` where applicable
  - Parse JSONL output into `{ file, line, text, matches: [ { start, end } ] }[]` grouped by file
  - Apply timeouts and support cancellation (child process kill)
  - Cap total results to avoid backpressure (e.g., 3k matches or 200 files)

- Edit/Apply Diff (new) — `src/main/tools/edit.ts`
  - `preview` (Phase 3 only): compute unified diff result and token counts (no writes). Return `{ type: 'preview', original: <clipped>, diff, modified: <clipped>, tokenCounts }`.
  - `apply` (Phase 4): gated by approval; perform safe writes via a new `writeTextFile` helper to be added to `file-service` in Phase 4. In Phase 3 always return `{ type: 'error', message: 'Apply requires approval (Phase 4)' }`.
  - Use line‑range safe application where possible; otherwise fail with actionable error (Phase 4).

- Terminal Stub (new) — `src/main/tools/terminal.ts`
  - Validate command string against basic allowlist/denylist (no execution in Phase 3)
  - Return `{ notImplemented: true }` to guide Phase 4; preserve input contract

- Renderer — Tool Call Visualization (new)
  - `src/components/agent-tool-calls.tsx`: minimal, accessible list of tool calls with per‑tool summaries
  - Update `src/components/agent-panel.tsx` to render tool calls below assistant messages when present. If the SDK does not expose `msg.toolCalls` directly, use the UI stream’s event metadata or an `onToolCall` callback (if available) to collect and render a compact list.

- Security
  - Validate all file paths; reject binary files on `file.read`
  - Sanitized ripgrep args (no arbitrary flags); disallow `-S` and other risky options; enforce reasonable query length and timeouts
  - Approval barrier for destructive actions (diff apply) and any shell execution (Phase 4)

- Dependencies
  - Ripgrep binary must be installed on the system (dev note and CI assumptions). Detect ENOENT and return a friendly error in the `search` tool.
  - No new npm runtime deps are required for Phase 3; node‑pty and xterm.js land in Phase 4


## Detailed Tasks & Steps

### 1) Tools Registry and Server Wiring

1.1 Create tools module
- File: `src/main/agent/tools.ts`
- Export `getAgentTools(deps)` returning the consolidated tools; deps include token service, path validator, ripgrep runner, diff helpers

Code example (skeleton):
```ts
// src/main/agent/tools.ts
import { z } from 'zod';
import { tool } from 'ai';
import { validateAndResolvePath, readTextFile } from '../file-service';
import { runRipgrepJson } from '../tools/ripgrep';
import { getMainTokenService } from '../../services/token-service-main';

export function getAgentTools() {
  const tokenService = getMainTokenService();

  const file = tool({
    description: 'Read file content within the current workspace',
    parameters: z.object({ path: z.string(), lines: z.object({ start: z.number().int().min(1), end: z.number().int().min(1) }).optional() }),
    execute: async ({ path, lines }) => {
      const val = validateAndResolvePath(path);
      if (!val.ok) throw new Error(val.message);
      const r = await readTextFile(val.absolutePath, { lines });
      if (!r.ok) throw new Error(r.message);
      const { count } = await tokenService.countTokens(r.content);
      return { path: val.absolutePath, content: r.content, tokenCount: count };
    },
  });

  const search = tool({
    description: 'Ripgrep code search with JSON results',
    parameters: z.object({ query: z.string().min(1).max(256), directory: z.string().optional(), maxResults: z.number().int().min(1).max(5000).optional() }),
    execute: async ({ query, directory, maxResults }) => {
      const out = await runRipgrepJson({ query, directory, maxResults });
      return out; // structured matches grouped by file
    },
  });

  const edit = tool({
    description: 'Preview or apply a unified diff to a file',
    parameters: z.object({ path: z.string(), diff: z.string(), apply: z.boolean().default(false) }),
    execute: async ({ path, diff, apply }) => {
      // Phase 3: return preview only when apply=false; apply=true requires Phase 4 approval gate
      if (!apply) {
        const val = validateAndResolvePath(path); if (!val.ok) throw new Error(val.message);
        // compute preview using unified-diff helpers (to be implemented)
        return { type: 'preview', path: val.absolutePath };
      }
      return { type: 'error', message: 'Apply requires approval and will be enabled in Phase 4' };
    },
  });

  const context = tool({
    description: 'Summarize provided dual-context (initial + dynamic) envelope',
    parameters: z.object({ envelope: z.any() }),
    execute: async ({ envelope }) => {
      // Summarize files/prompts counts; avoids large payloads
      const initFiles = envelope?.initial?.files?.length || 0;
      const dynFiles = envelope?.dynamic?.files?.length || 0;
      return { initialFiles: initFiles, dynamicFiles: dynFiles };
    },
  });

  const terminal = tool({
    description: 'Terminal execution (stubbed in Phase 3)',
    parameters: z.object({ command: z.string(), cwd: z.string().optional() }),
    execute: async () => ({ notImplemented: true }),
  });

  return { file, search, edit, context, terminal } as const;
}
```

1.2 Wire tools into the chat handler
- File: `src/main/api-route-handlers.ts`
- In `handleChat`, create `const tools = getAgentTools()` and pass to `streamText({ model, system, messages, tools })`
- Maintain existing auth and validation; ensure `pipeUIMessageStreamToResponse(res)` is used


### 2) Ripgrep Runner and Parsing

2.1 Implement ripgrep runner
- File: `src/main/tools/ripgrep.ts`
- Spawn `rg` with `--json --line-number --color never` and root directory from `getAllowedWorkspacePaths()` (or provided safe subdir)
- Parse line‑delimited JSON; collect matches into a structured shape
- Apply caps and timeouts; kill child process on timeout or client disconnect

Code example (runner outline):
```ts
import { spawn } from 'node:child_process';
import { getAllowedWorkspacePaths } from '../workspace-context';

export async function runRipgrepJson({ query, directory, maxResults = 3000 }: { query: string; directory?: string; maxResults?: number }) {
  const roots = getAllowedWorkspacePaths();
  if (!roots || roots.length === 0) throw new Error('No active workspace');
  const cwd = directory && roots.some(r => directory.startsWith(r)) ? directory : roots[0];

  const args = ['--json', '--line-number', '--color', 'never', query, cwd];
  const child = spawn('rg', args, { stdio: ['ignore', 'pipe', 'pipe'] });

  const files: Record<string, { path: string; matches: Array<{ line: number; text: string; ranges: Array<{ start: number; end: number }>}> }> = {};
  let count = 0;

  await new Promise<void>((resolve, reject) => {
    const timer = setTimeout(() => { try { child.kill('SIGKILL'); } catch {} reject(new Error('ripgrep timeout')); }, 15000);
    child.stdout.on('data', (buf) => {
      const lines = buf.toString('utf8').split('\n');
      for (const line of lines) {
        if (!line.trim()) continue;
        try {
          const obj = JSON.parse(line);
          if (obj.type === 'match') {
            const file = obj.data.path.text as string;
            const ln = obj.data.line_number as number;
            const text = obj.data.lines.text as string;
            const sub = (obj.data.submatches || []).map((m: any) => ({ start: m.start, end: m.end }));
            const bucket = files[file] || (files[file] = { path: file, matches: [] });
            bucket.matches.push({ line: ln, text, ranges: sub });
            if (++count >= maxResults) { try { child.kill('SIGKILL'); } catch {} }
          }
        } catch {
          // ignore invalid JSON lines
        }
      }
    });
    child.on('error', (e) => { clearTimeout(timer); reject(e); });
    child.on('close', () => { clearTimeout(timer); resolve(); });
  });

  return { files: Object.values(files), totalMatches: count };
}
```

2.2 Input validation and safety
- Guard max query length (<= 256), forbid queries with unescaped wildcard that explode match set
- Enforce maxResults and timeout; document prerequisites (ripgrep must be installed)
- Add fallbacks: return friendly error if `rg` is not found (ENOENT)


### 3) Edit Tool (Preview First)

3.1 Preview
- Accept `{ path, diff, apply: false }`
- Validate path; read current file; compute a preview using a unified diff helper (to be added) and return `original`, `diff`, `modified` (clipped) and token counts

3.2 Apply (gated)
- For Phase 3, always return `{ type: 'error', message: 'Apply requires approval (Phase 4)' }`
- In Phase 4, implement approval workflow and safe write using existing file service


### 4) Renderer: Tool Call Visualization

4.1 Minimal component
- File: `src/components/agent-tool-calls.tsx`
- Render a list with tool name, short summary, and a toggle to show details (collapsible)
- In `src/components/agent-panel.tsx`, render beneath assistant messages when tool calls exist (guarded; no heavy layouts)

4.2 Accessibility
- Use roles and labels for screen readers; keyboard toggle for collapsible sections


### 5) Integration With Dual‑Context

5.1 Context tool
- Expose a `context` tool to summarize the current envelope; useful as a first tool call the agent can invoke

5.2 System prompt remains summary‑only
- Continue using the Phase 2 system prompt; avoid embedding full content in system


## Testing Strategy

- Unit tests (main)
  - `ripgrep.ts`: parse JSON lines; cap counts; timeout behavior (simulate)
  - `tools.ts`: schema validation; path validation are enforced; edit preview returns shape

- Server handler tests
  - `api-chat-tools.test.ts`: mock `streamText` and assert `tools` registry is passed; error normalization (ENOENT -> friendly message)

- Renderer tests
  - `agent-tool-calls.test.tsx`: renders tool call summaries beneath assistant turns; toggles details

- Manual testing
  - Install ripgrep locally; run `npm run dev:electron` and prompt the agent to “search for TODOs in src/utils”
  - Validate rate limiting and timeouts by searching “.” or very broad queries


## Integration Points

- Server chat handler: `src/main/api-route-handlers.ts` `handleChat`
- Tools registry: `src/main/agent/tools.ts`
- Ripgrep runner: `src/main/tools/ripgrep.ts`
- File service and path validator: `src/main/file-service.ts`, `src/main/workspace-context.ts`
- Token service: `src/services/token-service-main.ts`
- Renderer messages: `src/components/agent-panel.tsx`
- Tool visualization: `src/components/agent-tool-calls.tsx`


## Potential Challenges & Mitigations

- Ripgrep availability: not installed on some developer machines
  - Mitigation: detect ENOENT and return a helpful error; document installation steps; skip ripgrep tests in CI or use fixtures

- Large outputs / backpressure
  - Mitigation: cap results and bytes; summarize top N matches with counts; allow the agent to ask to refine the query

- Path traversal / security
  - Mitigation: use `validateAndResolvePath` + `getAllowedWorkspacePaths()` for all file operations; drop out‑of‑workspace paths

- Token/latency budget
  - Mitigation: stream compact summaries; paginate results; avoid returning entire files via tools

- Diff correctness for apply
  - Mitigation: limit Phase 3 to preview only; add safe apply + approvals in Phase 4

- Parallel tool execution
  - Mitigation: keep Phase 3 serial per turn; add parallelism with care later


## Task Breakdown & Timeline

- Phase 3a (Tools + Ripgrep)
  - [ ] Add tools registry and wire into chat handler
  - [ ] Implement ripgrep runner and parsing with limits/timeouts
  - [ ] Implement file.read/info, context.summary tools
  - [ ] Implement edit.preview (apply gated)
  - [ ] Add renderer tool‑call visualization
  - [ ] Tests (ripgrep parsing, tools wiring, UI rendering)

- Phase 3b (Telemetry + Polish)
  - [ ] Add tool execution metrics (count, duration) to system prompt or metadata
  - [ ] Improve error surfaces for tool failures (user‑friendly banners)
  - [ ] Harden input schemas and limits


## Acceptance Criteria

- `/api/v1/chat` streams with a `tools` registry including `file`, `search`, `edit`, `context` (and a terminal stub)
- Ripgrep code search returns structured results within limits and timeouts
- Tool calls appear beneath assistant messages in the Agent Panel, with concise summaries
- All new tests pass; existing suites remain green
- Security checks prevent access outside workspace; destructive actions remain gated


## Follow‑Up: Phase 4 Preview (Terminal Manager)

- Implement `TerminalManager` with node‑pty in main process and IPC bridge
- Add xterm.js UI (tabs optional) and approval prompts for command execution
- Connect `terminal` tool to manager and stream output back to chat
- Enhance diff apply with approvals and file write safety checks


## Appendix A: Code Scaffolds (Do Not Implement Yet)

Quick Checklist (File Creation Order):
- [ ] Create `src/main/tools/ripgrep.ts` (runner + JSONL parsing skeleton)
- [ ] Create `src/main/agent/tools.ts` (consolidated tools registry)
- [ ] (Optional) Create `src/main/tools/edit.ts` and `src/main/tools/terminal.ts` stubs
- [ ] Wire tools into `src/main/api-route-handlers.ts` (`handleChat` → `streamText({ tools })`)
- [ ] Create `src/components/agent-tool-calls.tsx` (minimal tool-call UI)
- [ ] Add gated render in `src/components/agent-panel.tsx` to show tool calls (feature flag)
- [ ] Add server + renderer tests (ripgrep parsing, tools wiring, tool-call UI)

Test UI Toggle (for deterministic tests):
- Introduce (or reuse) a feature flag: `AGENT_TOOL_CALLS_UI` under the existing `FEATURES` mechanism.
  - Guard rendering in AgentPanel: only render `AgentToolCalls` when `(window as any).__PF_FEATURES?.AGENT_TOOL_CALLS_UI !== false`.
  - Default to `true` locally; in tests set it explicitly:
    ```ts
    (window as any).__PF_FEATURES = { ...(window as any).__PF_FEATURES, AGENT_TOOL_CALLS_UI: true };
    ```
- Alternative minimal approach: add a `hidden?: boolean` prop to `AgentToolCalls` and pass `hidden` in tests to suppress DOM noise. Prefer the feature-flag gate for consistency with existing patterns in the repo.

The following scaffolds are ready-to-copy outlines to accelerate Phase 3 delivery. Do not implement them now; they are included to clarify structure, imports, and interfaces.

### A1) Tools Registry Scaffold — `src/main/agent/tools.ts`

```ts
// src/main/agent/tools.ts
import { z } from 'zod';
import { tool } from 'ai';
import { validateAndResolvePath, readTextFile /*, writeTextFile */ } from '../file-service';
import { getMainTokenService } from '../../services/token-service-main';
import { runRipgrepJson } from '../tools/ripgrep';

export function getAgentTools() {
  const tokenService = getMainTokenService();

  const file = tool({
    description: 'Read file content within the current workspace',
    parameters: z.object({
      path: z.string(),
      lines: z
        .object({ start: z.number().int().min(1), end: z.number().int().min(1) })
        .refine((v) => v.end >= v.start, 'end must be >= start')
        .optional(),
    }),
    execute: async ({ path, lines }) => {
      const val = validateAndResolvePath(path);
      if (!val.ok) throw new Error(val.message);
      const r = await readTextFile(val.absolutePath, { lines });
      if (!r.ok) throw new Error(r.message);
      const { count } = await tokenService.countTokens(r.content);
      return { path: val.absolutePath, content: r.content, tokenCount: count };
    },
  });

  const search = tool({
    description: 'Ripgrep code search with JSON results',
    parameters: z.object({
      query: z.string().min(1).max(256),
      directory: z.string().optional(),
      maxResults: z.number().int().min(1).max(5000).optional(),
    }),
    execute: async ({ query, directory, maxResults }) => {
      return runRipgrepJson({ query, directory, maxResults });
    },
  });

  const edit = tool({
    description: 'Preview or apply a unified diff to a file',
    parameters: z.object({ path: z.string(), diff: z.string(), apply: z.boolean().default(false) }),
    execute: async ({ path, diff, apply }) => {
      if (!apply) {
        // Preview placeholder — real computation added in implementation
        const val = validateAndResolvePath(path);
        if (!val.ok) throw new Error(val.message);
        return { type: 'preview', path: val.absolutePath, diff, modifiedPreview: '(omitted)' };
      }
      return { type: 'error', message: 'Apply requires approval (Phase 4)' };
    },
  });

  const context = tool({
    description: 'Summarize provided dual-context (initial + dynamic) envelope',
    parameters: z.object({ envelope: z.any() }),
    execute: async ({ envelope }) => {
      const initFiles = envelope?.initial?.files?.length || 0;
      const dynFiles = envelope?.dynamic?.files?.length || 0;
      return { initialFiles: initFiles, dynamicFiles: dynFiles };
    },
  });

  const terminal = tool({
    description: 'Terminal execution (stubbed in Phase 3)',
    parameters: z.object({ command: z.string(), cwd: z.string().optional() }),
    execute: async () => ({ notImplemented: true }),
  });

  return { file, search, edit, context, terminal } as const;
}
```

Wiring example in `handleChat` (for later implementation):
```ts
// src/main/api-route-handlers.ts
import { getAgentTools } from './agent/tools';

const tools = getAgentTools();
const result = streamText({ model, system, messages: modelMessages, tools });
result.pipeUIMessageStreamToResponse(res);
```

Note: preserve the existing Phase 2 system prompt composition via `buildSystemPrompt(...)` and the current auth/validation middleware. Only add `tools` — do not alter message conversion or streaming response headers.

### A2) Ripgrep Runner Scaffold — `src/main/tools/ripgrep.ts`

```ts
// src/main/tools/ripgrep.ts
import { spawn } from 'node:child_process';
import { getAllowedWorkspacePaths } from '../workspace-context';

export async function runRipgrepJson({
  query,
  directory,
  maxResults = 3000,
}: {
  query: string;
  directory?: string;
  maxResults?: number;
}) {
  const roots = getAllowedWorkspacePaths();
  if (!roots || roots.length === 0) throw new Error('No active workspace');
  const cwd = directory && roots.some((r) => directory.startsWith(r)) ? directory : roots[0];

  const args = ['--json', '--line-number', '--color', 'never', query, cwd];
  const child = spawn('rg', args, { stdio: ['ignore', 'pipe', 'pipe'] });

  const files: Record<string, { path: string; matches: Array<{ line: number; text: string; ranges: Array<{ start: number; end: number }>} > }> = {};
  let count = 0;

  await new Promise<void>((resolve, reject) => {
    const timer = setTimeout(() => {
      try { child.kill('SIGKILL'); } catch {}
      reject(new Error('ripgrep timeout'));
    }, 15000);

    child.stdout.on('data', (buf) => {
      const lines = buf.toString('utf8').split('\n');
      for (const line of lines) {
        if (!line.trim()) continue;
        try {
          const obj = JSON.parse(line);
          if (obj.type === 'match') {
            const file = obj.data.path.text as string;
            const ln = obj.data.line_number as number;
            const text = obj.data.lines.text as string;
            const sub = (obj.data.submatches || []).map((m: any) => ({ start: m.start, end: m.end }));
            const bucket = files[file] || (files[file] = { path: file, matches: [] });
            bucket.matches.push({ line: ln, text, ranges: sub });
            if (++count >= maxResults) { try { child.kill('SIGKILL'); } catch {} }
          }
        } catch {
          // ignore invalid JSON lines
        }
      }
    });
    child.on('error', (e) => { clearTimeout(timer); reject(e); });
    child.on('close', () => { clearTimeout(timer); resolve(); });
  });

  return { files: Object.values(files), totalMatches: count };
}
```

Notes for implementation:
- Validate ripgrep availability and convert ENOENT to a friendly error.
- Consider `--ignore-file` if `.gitignore` path is available; otherwise rely on our ignore utils as a Phase 3b improvement.
- Enforce query length and other safety checks before spawning the process.

### A3) Minimal Tool‑Call UI Scaffold — `src/components/agent-tool-calls.tsx`

```tsx
// src/components/agent-tool-calls.tsx
import { memo, useState } from 'react';

export type ToolCall = {
  id: string;
  toolName: string;
  summary: string;
  details?: unknown;
};

export default memo(function AgentToolCalls({ calls = [] as ToolCall[] }) {
  if (!calls || calls.length === 0) return null;
  return (
    <div className="tool-calls" aria-label="Agent tool calls">
      {calls.map((c) => (
        <ToolCallRow key={c.id} call={c} />
      ))}
    </div>
  );
});

function ToolCallRow({ call }: { call: ToolCall }) {
  const [open, setOpen] = useState(false);
  return (
    <div className="tool-call">
      <div className="tool-header">
        <span className="tool-name">{call.toolName}</span>
        <button type="button" className="tool-toggle" onClick={() => setOpen((v) => !v)} aria-expanded={open}>
          {open ? 'Hide' : 'Show'}
        </button>
      </div>
      <div className="tool-summary">{call.summary}</div>
      {open && (
        <pre className="tool-details" aria-label="Tool details">
{JSON.stringify(call.details, null, 2)}
        </pre>
      )}
    </div>
  );
}
```

Wiring hint (later implementation):
- In `src/components/agent-panel.tsx`, when rendering assistant messages, detect SDK tool call metadata (e.g., `msg.toolCalls`) and map to `ToolCall[]` for `AgentToolCalls`.
- Keep this lightweight; avoid heavy layouts and large payload rendering by default (collapsed view).

### A4) Tests and Validation
- Main: add `src/main/__tests__/ripgrep-runner.test.ts` covering JSON parsing, caps, timeout, and ENOENT handling (mock `child_process.spawn`).
- Main: add `src/main/__tests__/agent-tools-wireup.test.ts` that stubs `getAgentTools()` and asserts `streamText` called with `tools`.
- Renderer: add `src/__tests__/agent-tool-calls-render.test.tsx` to verify `AgentToolCalls` renders summaries and toggles details.
- Renderer: ensure existing AgentPanel tests still pass; add one integration asserting `prepareSendMessagesRequest` body unchanged apart from `context` when tools enabled.

## Risks & Mitigations
- Missing renderer auth bridge will cause 401s from `/api/v1/chat`.
  - Mitigation: ship “0) Preflight & Dependencies” first; preload exposes `{ apiBase, authToken }`.
- `rg` not installed on developer machines/CI.
  - Mitigation: detect ENOENT and return a friendly tool result; document local install in README and CI image.
- Large result sets impacting stream size or UI.
  - Mitigation: cap matches/files; include truncated flags; consider pagination follow‑ups.
- Diff write safety for `edit.apply`.
  - Mitigation: keep apply disabled until Phase 4 with explicit approval and a `writeTextFile` helper in `file-service`.
