# File Tree Checkbox Unresponsiveness — Investigation Plan

## Summary
- Symptom: After opening a new folder containing a very large number of files, file tree checkboxes intermittently do not toggle when clicked. Loading a saved workspace of the same folder does not show this problem.
- Confirmed: This is not caused by the processing overlay or any CSS issue.
- Root cause: Early directory checkbox clicks happen before the folder index contains that directory’s files, so `toggleFolderSelection` computes a `noop` plan and returns without applying any optimistic state. Result: no folder checkmark and no files selected, despite a successful click.

## Repro & Observations
- Repro: Open a new large folder (very large file/dir counts). While the file list is streaming in, click file/folder checkboxes.
- Expected: Checkbox toggles immediately and deterministically.
- Actual: Clicks sometimes do not toggle; when they do, the state can lag significantly. Disabling the processing overlay (pointer-events) does not change behavior, ruling out CSS/overlay as a cause.
- Correlated: CPU usage spikes during streaming updates. The behavior improves once streaming completes.

## Code Path Review

### New Folder Flow
- User picks folder → Electron main sends `folder-selected`.
- Renderer handler constructs a minimal workspace and dispatches a direct open event:
  - `src/handlers/electron-handlers.ts:209` → `handleWorkspaceUpdate` (persists a new workspace)
  - `src/handlers/electron-handlers.ts:223` dispatches `directFolderOpened`
- Renderer applies workspace and starts streaming files:
  - `src/hooks/use-app-state.ts:1537` `handleDirectFolderOpenedEvent` → `applyWorkspaceData`
  - `applyWorkspaceData` detects folder change and calls `handleFolderChange` (`src/hooks/use-app-state.ts:934`)
  - `handleFolderChange` sets `processingStatus` to "processing" and sends `request-file-list` (`src/hooks/use-app-state.ts:971`)
- Streaming updates: file list and derived structures update incrementally while data arrives; the folder index may not yet contain a given directory at the exact time a user clicks its checkbox.

### Saved Workspace Flow
- Loading an existing workspace dispatches `workspaceLoaded` → `applyWorkspaceData` (`src/hooks/use-app-state.ts:1509` and `:1089`).
- Same `handleFolderChange` path triggers `request-file-list` and sets "processing".
- Important difference in timing: `applyWorkspaceData` defers applying saved details until streaming completes (`processingStatus === 'complete'`) and instructions are loaded (`src/hooks/use-app-state.ts:1574`), so users typically interact post‑stream when the main thread is less busy.
- Net effect: users click after the worst per-chunk recomputation has finished, so interactions seem responsive.

## Checkbox Handling Confirmed OK
- Tree item checkbox component and handlers are correct and stop propagation properly:
  - Component: `src/components/tree-item.tsx`
  - Checkbox markup: `TreeItemCheckbox` (`:168`–`:206`) sets `onChange` and stops propagation
  - Handlers: `handleCheckboxChange` toggles files or directories (`:506`–`:528`)
- Virtualization (`src/components/virtualized-tree.tsx`) doesn’t block events; it only renders rows.
- Sidebar header “Select All” is intentionally disabled during selection-overlay recompute, but item checkboxes aren’t disabled by that path.

## Root Cause Analysis (Updated)
- Overlay/CSS: Ruled out experimentally.
- Early folder toggle no-op (confirmed):
  - When a directory checkbox is clicked very early in streaming, the folder index often doesn’t contain that directory yet.
  - Evidence from logs: `getFilesInFolder` returns `[]` (lookup count 0, hasKey false), then `toggleFolderSelection` computes `{ kind: 'noop' }` and returns.
  - Current logic returns before applying any optimistic state to the directory’s checkbox; nothing appears selected.
- Why saved workspaces work: Interactions typically happen after streaming completes, so the folder index contains the directory and the computed plan performs the expected add/remove.

Additional confirmed finding: Early folder toggles compute a no-op plan
- When a directory checkbox is clicked very early in streaming, the folder index often doesn’t contain that directory yet.
  - Evidence from logs: `[folder-index] lookup ... count: 0, hasKey: false`, and then `[folder-toggle] request ... selectableCount: 0` followed by `[folder-toggle] plan { kind: 'noop' }`.
- Current logic returns before applying any optimistic state:
  - In `toggleFolderSelection`, if the computed plan is `noop`, it returns immediately and never calls the optimistic update (which would visually mark the directory selected while children stream in).
- Net impact: The user sees no checkmark on the folder and no files selected, even though they clicked the checkbox; this is not CSS, it’s a true no‑op due to an empty selectable set at that moment.

## Additional Findings (Performance/Scale)
- Large datasets: Overlay worker is used progressively for selection states, but the upfront directory structure keying/build still occurs on the main thread and can be expensive while streaming.
- Virtualization overscan adapts by scroll velocity (`src/components/virtualized-tree.tsx:115`–`:147`) and isn’t implicated in event capture; its cost is minimal compared to repeated global sorts and directory rebuilds.

## Proposed Resolution Strategy

Goal: Ensure directory toggles work deterministically even when clicked before the folder index is populated.

1) Fix early folder toggle no-op
- In `toggleFolderSelection` (renderer):
  - If `selectableFiles.length === 0`:
    - For select: apply optimistic state to the directory checkbox (mark as `full`) and record a pending “add” intent.
    - For deselect: apply optimistic `none` and record a pending “remove” intent.
  - Defer actual add/remove of files until the index for that folder becomes non-empty, then compute and apply the real plan (existing chunked logic).
- Trigger re-evaluation of pending intents when new file-list chunks arrive (existing `file-list-updated` event) or on a small debounce of `allFiles.length` changes.

6) Fix early folder toggle no-op
- In `toggleFolderSelection` (renderer):
  - If `selectableFiles.length === 0` and `isSelected === true`, perform an optimistic directory update anyway (mark folder as `full`) and enqueue a deferred apply that re-evaluates once the index reports files for that folder (e.g., on next file-list chunk or after a short debounce).
  - Similarly, for deselect with empty set, mark optimistic `none` so the folder visually unchecks, with a deferred remove pass when files stream in.
- This preserves the user’s intent even when streaming hasn’t surfaced the folder’s files yet.

## Implementation Steps (No code changes yet)
1) Implement optimistic directory state and pending intent tracking in `use-file-selection-state.ts`.
2) Re-evaluate pending folders on `file-list-updated` or debounced `allFiles.length` change, apply the computed add/remove plan, and clear the pending item.
3) Ensure directory selection cache reflects optimistic state immediately so users see visual feedback.

## Risks & Considerations
- Directory-selection states may briefly report “none”/“partial” while actual add/remove applies; ensure cache updates are consistent during the deferred phase.
- Ensure saved-workspace flow remains stable; the new optimistic path must not alter persisted selection replay.

## Appendix: Key References
- Sidebar and tree components:
  - `src/components/sidebar.tsx`
  - `src/components/virtualized-tree.tsx`
  - `src/components/tree-item.tsx`
- New folder flow:
  - `src/handlers/electron-handlers.ts:209`, `:223`, `:628`
  - `src/hooks/use-app-state.ts:1537`, `:934`, `:971`
- Saved workspace flow:
  - `src/hooks/use-app-state.ts:1509`, `:1089`
- Streaming updates:
  - `src/handlers/electron-handlers.ts:600–740` (file-list data handling and per-chunk `applyFiltersAndSort`)
  - `src/handlers/filter-handlers.ts` (`applyFiltersAndSort` sorts per chunk)
- Selection cache rebuilds:
  - `src/utils/selection-cache.ts` (`computeFilesKey`, `buildDirectoryStructure`, progressive overlay interface)

## Final Resolution Plan (Ready To Implement)

1) Fix early folder toggle no-op (optimistic apply + deferred real apply)
- File: `src/hooks/use-file-selection-state.ts`
- Change: In `toggleFolderSelection`:
  - If `selectableFiles.length === 0` and `isSelected === true`, do NOT return `noop`. Instead:
    - Apply optimistic state in the `folderSelectionCache` for `folderPath` to `'full'` immediately.
    - Record a pending toggle intent (e.g., `pendingFolderTogglesRef: Map<string, 'add'|'remove'>`).
  - If `isSelected === false` and `selectableFiles.length === 0`, optimistically set `'none'` and record a `'remove'` intent.
  - Add a debounced re-evaluator (150–250ms) that re-runs `computeFolderTogglePlan` for pending folders once the `folderIndex` reports files for that folder, and applies the real add/remove plan (chunked as today).
- Trigger points to re-evaluate pending toggles:
  - Listen for the existing `file-list-updated` CustomEvent fired by the streaming handler (`src/handlers/electron-handlers.ts`, at the end of `createFileListDataHandler`).
  - Also re-check on `allFiles` length changes (as a fallback) with a small debounce to avoid thrash.
- Rationale: Preserves user intent immediately and completes the operation as soon as data arrives. Prevents the silent no-op path.

2) Tests and acceptance criteria
- Unit/logic tests:
  - Early folder toggle while index is empty → optimistic folder check applied; once files appear, the selection fills accordingly.
  - Deselect on empty index → optimistic uncheck applied; selection removed when files appear.
  - Saved workspace: No change in behavior; selections remain deterministic.
- Manual acceptance:
  - While streaming a large new folder, clicking a folder’s checkbox immediately sets it to checked/indeterminate and eventually selects its files without additional clicks.
  - Repeated rapid toggles do not regress; directory state remains consistent and completes after data arrives.

3) Backout strategy
- If the deferred apply causes unexpected states in rare edge cases, remove the pending-intent path and revert to current behavior. The change is localized to `toggleFolderSelection` and the re-evaluation hook.

## Implementation Notes (Where/How)
- Apply optimistic state through the existing `folderSelectionCache.set(folderPath, 'full'|'none')` and bump the cache version (`setManualCacheVersion`) so directory checkboxes reflect state instantly.
- Maintain a `pendingFolderTogglesRef: Map<string, { action: 'add'|'remove', ts: number }>` in `use-file-selection-state.ts`.
- On `file-list-updated` (or a 200ms debounce on `allFiles.length`), for each pending folder:
  - Fetch `selectableFiles = getFilesInFolder(folderIndex, folderPath)`.
  - Compute plan; if still empty, keep pending (with time cap to avoid infinite retries).
  - Apply chunked add/remove, then clear the pending item.
- Ensure bulk selectors and select-all behavior continue to use the chunked strategy.
