# PasteFlow — Cost Calculation: Review and Simplest Path Forward (2025‑09‑06)

This note compares Cline’s cost tracking approach with PasteFlow’s current state and proposes the simplest reliable way to add costs to PasteFlow. No code changes are made here; this is an implementation analysis and plan.

## TL;DR

- PasteFlow today has only a rough UI‑side estimate for cost in the Agent panel, based on a tiny heuristic map of per‑1K token rates. It is not persisted and ignores provider nuances (cache tokens, thinking tokens, subscription vs pay‑per‑use, etc.).
- Cline computes costs in provider layers using model pricing metadata, handles cache read/write tokens and provider quirks, and surfaces costs in UI badges and history summaries.
- For PasteFlow, the simplest robust path is:
  1) Define a small pricing table (per provider+model) in main process (server). Keep prices “per 1M tokens” to match vendor docs and Cline.
  2) During `onFinish` (server), compute cost from actual usage (input/output; optionally cache/think tokens when available) and persist alongside our usage row.
  3) Expose usage rows (now including `cost_usd`) via IPC. UI just displays cost that is already computed, falling back to a conservative estimate only when usage is missing.
  4) Aggregate per‑session cost in Model Settings (“Session Tokens” → add “Session Cost”), and per‑message cost on assistant rows.

This keeps complexity low, avoids drifting client‑side heuristics, and aligns with how Cline succeeds: provider‑aware computation at the source with a shared pricing table.

---

## What Cline Does (Summary)

- Maintains model pricing in shared config (per 1M tokens): `inputPrice`, `outputPrice`, and optionally `cacheWritesPrice`, `cacheReadsPrice`, plus special cases (e.g., DeepSeek’s zero input price) and provider‑specific attributes (supportsPromptCache) [shared/api.ts].
- Computes cost inside provider adapters using usage data emitted during streaming:
  - Anthropic: `calculateApiCostAnthropic(info, input, output, cacheWrites, cacheReads)` → `cacheWrites + cacheReads + input + output` components.
  - OpenAI & others: `calculateApiCostOpenAI(info, input, output, cacheWrites, cacheReads)` → subtract cached tokens from input (avoid double counting) + add cache read/write costs.
  - Gemini: adds “thinking” tokens and tiered pricing; includes cache reads cost.
  - Claude Code: sets totalCost to 0 for subscription usage (non‑pay‑per‑use) vs paid usage.
- Surfaces cost to UI:
  - Per request badges in chat rows, formatted `$${cost.toFixed(4)}`.
  - Aggregated cost in history views (task total cost).
- Accumulates across steps; supports a separate credits system in some flows.

## PasteFlow Today (State)

- Telemetry DB: `usage_summary(session_id, input_tokens, output_tokens, total_tokens, latency_ms)` — no `cost_usd` column.
- Server: Persists tokens/latency on `onFinish`; no cost calculation.
- UI:
  - Header chip uses a tiny heuristic (`estimateCostUSD` in `AgentPanel`) with a few string matches (e.g., `gpt-4o-mini`, `gpt-5`, `haiku`) and per‑1K rates. Cost appears only for recognized models and only inline (not persisted).
  - No provider‑specific adjustments (cache read/write tokens, thinking tokens, subscription guards).
  - No session cost aggregation.

## Gaps vs Cline

| Topic | Cline | PasteFlow |
|---|---|---|
| Pricing source | Central, per model, per 1M | Minimal, heuristic per 1K in UI only |
| Provider nuances | Handles cache read/write; thinking tokens; subscription | Not handled |
| Compute location | Provider layer during streaming `onFinish` | Renderer heuristic; not persisted |
| Storage | Persists totalCost (surfaced to history) | No `cost` stored |
| Display | Per‑request badges; totals in history | Not displayed consistently; header/message heuristics only |

## Data We Have / Will Have

- Immediately available now: `inputTokens`, `outputTokens` from SDK `onFinish` for most providers. Sometimes absent.
- Sometimes available now or later: `cacheWriteTokens`, `cacheReadTokens`, `thinkingTokens` (provider‑specific; often not provided by generic SDKs).
- We already persist: `latency_ms`.

## Simplest Path for PasteFlow (Pragmatic & Incremental)

1) Add a small pricing table in main (server)
   - A simple `pricing.ts` module that exports per‑provider/model entries. Use “per 1M tokens” rates (industry standard, same as Cline). Example:
   ```ts
   export type Pricing = { inPerMTok: number; outPerMTok: number; cacheWritePerMTok?: number; cacheReadPerMTok?: number; thinkPerMTok?: number; subscriptionFree?: boolean };
   export const PRICING: Record<string, Pricing> = {
     'openai:gpt-4o-mini': { inPerMTok: 5, outPerMTok: 15 },
     'anthropic:claude-3-5-haiku-20241022': { inPerMTok: 3, outPerMTok: 15, cacheReadPerMTok: 0.3, cacheWritePerMTok: 3.75 },
     // Add a few first‑class SKUs actually used in app; expand over time.
   };
   ```
   - Normalize model ids to a canonical key (`provider:modelId`) using our existing resolver (we already do similar for models).

2) Server‑side cost computation in `onFinish`
   - In the same place we persist `usage_summary`, compute `cost_usd`:
     - `cost = inPerMTok*(inputUncached/1e6) + outPerMTok*(output/1e6) + cacheReadPerMTok*(cacheReads/1e6) + cacheWritePerMTok*(cacheWrites/1e6) + thinkPerMTok*(thinking/1e6)`.
     - Start simple: if we don’t receive cache/think tokens, treat them as 0; don’t attempt to infer.
     - For OpenAI‑style caching, if both cached read/write token counts are present, subtract them from input for the “uncached” portion (Cline’s `calculateApiCostOpenAI`). If not present, skip subtraction.
     - If a model is flagged `subscriptionFree`, set `cost = 0`.

3) Persist cost
   - Add nullable `cost_usd REAL` to `usage_summary` (idempotent migration), write best‑effort value.
   - Expose in `agent:usage:list` IPC so the renderer can render without heuristics.

4) UI display (reuse our existing surfaces)
   - Assistant message row: show `cost_usd` if present; if absent but input/output tokens exist and we have pricing, compute a one‑off estimate client‑side (mark “approx”).
   - Header chip: optionally omit cost (current product choice) or show a subtle cost badge; for now, keep cost only in per‑message tooltip (already planned).
   - Model Settings: aggregate session cost by summing `cost_usd` per row; show as “Session Cost: $X.XXXX”.

5) Fallbacks and clarity
   - When providers don’t return usage tokens, we keep our existing token estimation for UX continuity, but clearly label as “(approx)” and do not persist approximate costs as authoritative.
   - All persisted costs originate from server calculations only (deterministic, auditable via pricing table).

## Why This Is the Simplest Reliable Approach

- Keeps price logic centralized and provider‑aware (server), not duplicated in UI.
- Starts with minimal pricing entries for the few models we actually ship by default, avoiding a large catalogue at day one.
- Avoids complex cache/think token inference; we use them only when provided by the SDK/provider.
- UI remains simple: render what’s persisted, with a small client‑side estimate only when necessary for UX, always labeled as approximate.

## Edge Cases & Notes

- OpenRouter and other routers: normalize to underlying model where possible; otherwise attach pricing by the routed id we actually requested (e.g., `openrouter:openai/gpt-4o-mini`).
- Model id drift: if a model id isn’t found in pricing, skip cost and show “—” (or approximate if tokens exist and we want a hint); log once in dev.
- Currency: USD only; no tax. (Matches Cline’s formatting.)
- Units: Store prices per 1M tokens to match vendor docs; compute as `price * tokens / 1e6`.
- Rounding: Show `$X.XXXX` in UI; store full‑precision in DB (`REAL`).

## Implementation Sketch (Phases)

Phase A (1 day):
- Add `cost_usd` column to `usage_summary` (nullable, idempotent migration).
- Add `pricing.ts` with 3–5 SKUs we support.
- Compute and persist `cost_usd` in `onFinish` when input/output present.
- Expose cost via `agent:usage:list` IPC.

Phase B (0.5 day):
- Render `cost_usd` in assistant message tooltip and (optionally) inline small badge.
- Aggregate session cost and show in Model Settings.

Phase C (later):
- Add cache/think token handling when SDK exposes them.
- Expand pricing table; optionally load from JSON to ease updates.
- Add per‑provider tests to validate a few cost formulas.

## Comparison to Our Current Heuristic

| Aspect | Current Heuristic (UI) | Proposed Minimal Server Design |
|---|---|---|
| Source | Hard‑coded includes() on `modelId` | Canonicalized `provider:modelId` key |
| Rates | Few per‑1K guesses | Per‑1M vendor rates matching Cline |
| Where computed | Renderer (per render) | Server (`onFinish`), persisted |
| Cache/think tokens | Ignored | Used when present; otherwise 0 |
| Subscription | Not handled | `subscriptionFree` flag ⇒ cost 0 |
| Display | Ad‑hoc, not persisted | Per‑message from DB; session aggregate |

## Conclusion

Adopting a tiny, server‑side pricing table and computing `cost_usd` at `onFinish` gives PasteFlow a Cline‑like cost pipeline with minimal effort. We keep the UI simple (render persisted values) and retain our current estimation only as a fallback hint when usage data is missing. This approach is incremental, low risk, and accurate enough for real‑world usage.

