# Agent Panel @-mention Autocomplete — Investigation & Implementation Plan (Phase 1)

Summary
- Fix validated: the runtime “setInput is not a function” error in the Agent Panel was eliminated by decoupling the chat composer from the SDK’s input binding and using a local state-driven composer.
- Main content area already implements a robust @-path autocomplete for the instructions textarea; this behavior must be preserved.
- Agent Panel currently shows only a placeholder; an independent @-mention system must be implemented that:
  - Builds suggestions from the workspace file list
  - Adds selected files to Agent-local attachment stores (pending/pinned)
  - Attaches those as per-turn context on send via useChat’s hooks
  - Does not mutate or rely on the main Content Area’s selection state

Key file references for current behavior
- Agent runtime fix and scaffold
  - Agent Panel component: [AgentPanel()](src/components/agent-panel.tsx:26)
  - useChat hook usage: [useChat(...)](src/components/agent-panel.tsx:36)
  - Local composer state usage: [setComposer()](src/components/agent-panel.tsx:142)
  - Submit logic: [handleSubmit()](src/components/agent-panel.tsx:54)
- Main content area @-autocomplete
  - Query extraction: [computeQueryFromValue()](src/components/content-area.tsx:19)
  - Input component: [InstructionsTextareaWithPathAutocomplete()](src/components/content-area.tsx:78)
  - Caret-positioning helper: [getCursorCoordinates()](src/components/content-area.tsx:200)
  - Selection insert handler: [acceptSelection()](src/components/content-area.tsx:293)
  - Dropdown visuals (shared styles today): [content-area.css](src/components/content-area.css) — classes .autocomplete-container and .autocomplete-dropdown
- App shell where Agent Panel mounts
  - App shell: [App()](src/index.tsx:22)
  - Agent panel mount point: [AgentPanel](src/index.tsx:182)

Part 1 — Investigation Findings

1) Main content area “@-mention” system
- What it is:
  - A local, inline component [InstructionsTextareaWithPathAutocomplete()](src/components/content-area.tsx:78) bound to the “Docs/Instructions” textarea.
  - It detects “@” + token using [computeQueryFromValue()](src/components/content-area.tsx:19), filters file items from props (allFiles), and renders a caret-anchored dropdown with keyboard navigation and mouse selection.
  - Selecting an item inserts a formatted reference into the textarea via [acceptSelection()](src/components/content-area.tsx:293), and calls onSelectFilePath to update main selection (+ ensure tree folders expand to reveal the file).
- Inputs and side effects:
  - Receives allFiles, selectedFolder, expandedNodes, toggleExpanded, and handlers from ContentArea props.
  - Performs tree expansion and selection updates via callbacks (must NOT be reused in Agent Panel).
- Styling:
  - Uses shared dropdown styles in [content-area.css](src/components/content-area.css) for .autocomplete-container and .autocomplete-dropdown with ARIA attributes and a live region.

2) Agent Panel
- Current state:
  - Minimal chat UI with local composer; streaming/stop/clear and a pin toggle; no @-autocomplete yet.
  - Attachment stores are present in code shape (pending/pinned) but not wired to a mention dropdown.
- Key detail:
  - useChat is used with only { messages, sendMessage, status, stop }. The composer is owned by AgentPanel and updated via [setComposer()](src/components/agent-panel.tsx:142).
- Runtime fix status:
  - The previous error referenced a missing setInput. The component no longer calls SDK’s setInput and uses local state. The failing path is resolved.

Part 2 — Requirements Mapping (Phase 1 Guide alignment)
- Absolute separation of Agent attachments and mentions:
  - Agent mentions should populate Agent-local stores and context only; they must not toggle main selections or update the sidebar tree.
- Shared visuals, separate logic:
  - The Agent panel may reuse dropdown CSS classes for consistency but should locate logic and state under Agent-specific components and stores.
- Attachment behavior per Phase 1:
  - pendingAttachments apply to the next send only; pinnedAttachments persist per thread when “Pin” is enabled.
  - onBeforeSend composes context = pending + pinned (deduped).
  - onFinish clears pending; respect pin state for pinned.

Part 3 — Design: Separate Agent @-mention system

New components (renderer)
- Agent chat input with mentions
  - [AgentChatInputWithMention()](src/components/agent-chat-input.tsx:1) — A React component specialized for the Agent composer
    - Props:
      - value: string
      - onChange: (v: string) => void
      - disabled: boolean
      - allFiles: FileData[]
      - selectedFolder: string | null
      - onFileMention: (absPath: string, lines?: { start: number; end: number } | null) => void
    - Behavior:
      - Detects “@” pattern and opens an Agent-local dropdown near the caret
      - On selection, inserts a stable reference token into the composer (e.g., `@path/to/file.ext`), closes the dropdown, and invokes onFileMention(absPath) to add the file to pendingAttachments.
      - No calls to toggleFileSelection or toggleExpanded.
- Autocomplete popup for Agent
  - [AgentFileAutocomplete()](src/components/agent-file-autocomplete.tsx:1)
    - Props:
      - query: string
      - items: Array<{ abs: string; rel: string; relLower: string; size?: number; tokenCount?: number }>
      - position: { left: number; top: number }
      - onSelect: (item) => void
      - onClose: () => void
    - Behavior:
      - Filters and ranks items by partial match against relLower
      - Arrow/Enter/Tab/Escape keyboard handling
      - Optionally shows a hover preview panel later (Phase 1 can stub preview)
    - Styling:
      - Reuse .autocomplete-dropdown and .autocomplete-item styles to remain visually consistent, but scope container within the Agent panel and ensure z-index does not exceed the panel overlay.
- Attachment chips/cards (Agent-local)
  - [AgentAttachmentList()](src/components/agent-attachment-list.tsx:1)
    - Props:
      - pending: Map<string, AgentAttachment>
      - pinned: Map<string, AgentAttachment>
      - pinEnabled: boolean
      - onRemove: (absPath: string) => void
      - onPinToggle: (absPath: string, on: boolean) => void
    - Displays message-scoped attachments as chips/cards; remove/pin controls.
    - No cross-communication with main selection state.

Agent Panel integration
- [AgentPanel()](src/components/agent-panel.tsx:26)
  - New optional props:
    - allFiles?: FileData[]
    - selectedFolder?: string | null
  - State:
    - pendingAttachments: Map<string, AgentAttachment>
    - pinnedAttachments: Map<string, AgentAttachment>
    - pinEnabled: boolean
    - composer: string
  - Data flow:
    - Render AgentChatInputWithMention with { value: composer, onChange: setComposer, allFiles, selectedFolder, onFileMention }
    - onFileMention(abs):
      - Load or synthesize a minimal file context record for the pending map:
        - { path: abs, lines: null, content?: string, tokenCount?: number }
        - Safe file content load for preview/token counts can be added later; Phase 1 can attach only path or pre-provided token estimates when available.
    - onBeforeSend (via useChat options) composes context from pending + pinned (deduped) and returns it under message.metadata.context or directly on message as required by the route.
    - onFinish clears pending; respects pinEnabled for pinned.

Where to source file index for Agent suggestions
- Preferred for Phase 1: pass read-only file list from app state down to Agent Panel
  - At call site in the app shell:
    - [AgentPanel](src/index.tsx:182) should be updated to:
      - <AgentPanel allFiles={appState.allFiles} selectedFolder={appState.selectedFolder} />
    - Update [AgentPanelProps](src/components/agent-panel.tsx:13) to include those optional props to maintain backwards compatibility with existing tests and storybook setups.
- Alternative (not Phase 1): provide a small API route to fetch workspace index; keep for later phases if needed.

Part 4 — Step-by-step Implementation Plan

Step A — Preserve existing functionality
- No code changes to the main content autocomplete:
  - Keep [InstructionsTextareaWithPathAutocomplete()](src/components/content-area.tsx:78) and [computeQueryFromValue()](src/components/content-area.tsx:19) as-is.
  - Do not change [content-area.css](src/components/content-area.css) classes; Agent will reuse visuals.

Step B — Introduce Agent input with mentions
- Create [agent-chat-input.tsx](src/components/agent-chat-input.tsx)
  - Clone the minimal detection approach from ContentArea:
    - Copy/adjust [computeQueryFromValue()](src/components/content-area.tsx:19) to an internal utility in this file; scope it locally.
    - Copy/adjust caret positioning logic modeled after [getCursorCoordinates()](src/components/content-area.tsx:200), but ensure no external callbacks (e.g., tree expansion).
  - Render a textarea with onChange/onKeyDown wiring:
    - onChange: detect @ query, open dropdown, set caret anchor
    - onKeyDown: ArrowUp/Down/Enter/Tab/Escape handling delegated to the dropdown component
  - On selection:
    - Replace the “@token” span with a stable mention token, e.g., @rel/path or `@rel/path`. Prefer to keep consistency with main UI and use backticks or at-mention consistently within Agent messages (choose: “@abs rel path” or “`rel`”); for Agent we will insert “@rel/path” to visually convey Agent-specific mentions.
    - Call onFileMention(absPath) and keep focus.

Step C — Add Agent autocomplete dropdown
- Create [agent-file-autocomplete.tsx](src/components/agent-file-autocomplete.tsx)
  - Input props: query, items, position, onSelect, onClose
  - Build an items array once per allFiles change in parent (AgentChatInputWithMention):
    - Shape: { abs, rel, relLower }, where rel is computed relative to selectedFolder (if present); otherwise fallback to normalized absolute or project-relative path.
  - Filter using query.toLowerCase() against relLower, sort by shortest rel then lexicographic, limit to 10–12 results as in Content Area.
  - Manage keyboard focus and calls to onSelect with item payload.

Step D — Wire into Agent Panel
- Update [AgentPanelProps](src/components/agent-panel.tsx:13) to include:
  - allFiles?: FileData[]
  - selectedFolder?: string | null
- Update [AgentPanel](src/components/agent-panel.tsx:26) render:
  - Render new [AgentChatInputWithMention()](src/components/agent-chat-input.tsx:1) in place of the basic textarea
  - Pass the composer value, setter, allFiles, selectedFolder, and onFileMention handler
  - Place [AgentAttachmentList()](src/components/agent-attachment-list.tsx:1) above the message list to show chips/cards for pending + pinned
- Update [handleSubmit()](src/components/agent-panel.tsx:54) to leave the composer integration intact; only the input component changes.

Step E — Context assembly for send
- Continue using the byPath Map at send time to build context for the message:
  - See [handleSubmit()](src/components/agent-panel.tsx:54); ensure the composed context includes any pinned + pending attachments (deduped).
- Keep onFinish clearing pending and respecting pinEnabled.

Step F — CSS and layout
- Agent Panel already isolates styles in [agent-panel.css](src/components/agent-panel.css).
- For autocomplete visuals:
  - Prefer reusing class names .autocomplete-dropdown and .autocomplete-item to keep consistent visual styling; wrap in a container within the Agent Panel to ensure stacking context and avoid collision.
  - Optionally add .agent-autocomplete-container class wrapper in the Agent input component to scope layout without changing base dropdown styling.

Step G — App shell changes (read-only data feed)
- Modify [AgentPanel](src/index.tsx:182) invocation to:
  - <AgentPanel allFiles={appState.allFiles} selectedFolder={appState.selectedFolder} />
- Maintain backward compatibility: keep props optional and default to [] and null in Agent panel if not provided (e.g., tests that don’t mount full app state).

Step H — Tests
- Add renderer tests:
  - agent-mention-autocomplete.test.tsx
    - Mount App and type “@x” into Agent composer; expect dropdown to appear with filtered items from allFiles; select with keyboard Enter; verify composer value updated with inserted token.
    - Ensure no calls to toggleFileSelection or toggleExpanded occurred (spy appState methods when possible).
  - agent-attachments-store.test.tsx
    - Select a file from Agent dropdown; ensure it appears in pending attachments; on send, ensure pending clears and pinned persists when toggle is on.
  - Non-regression: content-area-instructions-autocomplete.test.tsx
    - Assert the existing behavior remains unchanged.

Step I — Accessibility and safeguards
- Ensure ARIA attributes mirror Content Area:
  - role="listbox", aria-expanded, aria-activedescendant, live region, and description guidance.
- No global key handlers: scope to the Agent textarea and dropdown container.
- Use useEffect cleanup for any document/window listeners added for caret repositioning or outside-click to close.

Part 5 — Acceptance Criteria

- Main Content Area
  - The instructions textarea continues to render and function with @-autocomplete exactly as before. No code changes to [InstructionsTextareaWithPathAutocomplete()](src/components/content-area.tsx:78) or its callers.
- Agent Panel
  - Typing “@” in the Agent composer opens a dropdown positioned at the caret; selection inserts the mention token into composer and calls onFileMention.
  - Selected mentions create entries in Agent-local pendingAttachments store; chips/cards render and can be removed; pinning persists across turns when “Pin” is on.
  - onBeforeSend composes context from pending + pinned (deduped), attached to the outgoing message; onFinish clears pending (and pinned if Pin is off).
- Separation
  - No calls from Agent components to toggleFileSelection(), toggleFolderSelection(), or toggleExpanded(). The file tree and Content Area behavior remain unaffected.
- Styling and accessibility
  - Autocomplete dropdown matches the look and feel of the Content Area (.autocomplete-dropdown, .autocomplete-item) while remaining contained within the Agent Panel.
  - ARIA attributes provide accessible navigation and announcements.
- Tests
  - New Agent-specific tests pass; existing Content Area tests remain green.
  - The existing smoke test for Agent panel input interaction continues to pass.

Part 6 — Notes on the Runtime Fix (“setInput is not a function”)

- Root cause
  - Code previously assumed useChat provided setInput. In @ai-sdk/react v2, the component should not rely on setInput availability, and local input management is the recommended approach in this project.
- Implemented fix
  - Use local composer state wired to the textarea:
    - Local composer variable and [setComposer()](src/components/agent-panel.tsx:142) handle input changes.
    - Sending uses [handleSubmit()](src/components/agent-panel.tsx:54) which reads composer.trim() and calls sendMessage with metadata containing Agent-local context.
  - SDK interaction
    - useChat is consumed for messages/send/stop/status; no reliance on setInput.
- Test coverage
  - See the added smoke test ensuring typing works: src/__tests__/agent-panel-input-interaction.test.tsx
  - The test verifies the composer remains controlled and accepts input without throwing.

Deliverables Checklist
- [ ] New components:
  - [AgentChatInputWithMention()](src/components/agent-chat-input.tsx:1)
  - [AgentFileAutocomplete()](src/components/agent-file-autocomplete.tsx:1)
  - [AgentAttachmentList()](src/components/agent-attachment-list.tsx:1)
- [ ] Update [AgentPanelProps](src/components/agent-panel.tsx:13) to accept allFiles and selectedFolder (optional).
- [ ] Update call site: [AgentPanel](src/index.tsx:182) with allFiles and selectedFolder.
- [ ] Add tests “agent-mention-autocomplete” and “agent-attachments-store”.
- [ ] Validate no regressions in Content Area tests.

Open Questions (non-blocking for Phase 1)
- Insertion token shape:
  - Keep parity with main (backtick-wrapped `rel/path`) or make Agent mentions distinct (e.g., @rel/path). Proposal: use @rel/path in Agent to be explicit about Agent-specific context mentions, but can align later if UX prefers backticks.
- Token estimates for attachment chips:
  - Phase 1 can show a lightweight hint (unknown or 0) and populate later via a safe file-read utility when available in renderer.