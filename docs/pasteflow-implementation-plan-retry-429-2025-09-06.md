# Implementation Plan — Server-side 429 Retry with Exponential Backoff — 2025-09-06

## Scope Summary
- Add a small, reusable retry helper for 429/5xx errors with jitter and `Retry-After` support.
- Wrap the stream creation path (pre-pipe) in retries to reduce user-facing rate-limit failures.
- Keep retries conservative (max 3) and never retry after headers are sent.

## Current Code Analysis
- Chat handler: `src/main/api-route-handlers.ts:262` (`APIRouteHandlers.handleChat`).
  - Stream creation: `streamText({...})` at `:389` (main) and `:469` (fallback without tools).
  - Response piping: `pipeUIMessageStreamToResponse` at `:403` and `:480`.
  - Error mapping: catches and maps provider status; `getStatusFromAIError` at `:1039`.
  - Backpressure guard exists for agent tool calls (not provider-level rate-limits) at `:331` returning 429.
- Config: `src/main/agent/config.ts` defines agent preferences and env overrides, but no retry config yet.
- Utilities: No existing generic retry utility module in `src/main`.

## Design Overview
- Introduce `withRateLimitRetries` helper that retries functions on 429 and specific transient 5xx statuses.
- Only wrap the “stream creation” section; do not retry after piping begins.
- Respect `Retry-After` when provided, supporting seconds or HTTP-date formats; otherwise exponential backoff with full jitter.
- Make retry count/delays configurable via env/prefs with sensible defaults.

## Step-by-Step Implementation
1) Add retry utility
- File: `src/main/utils/retry.ts` (new)
- Exports:
  - `export async function withRateLimitRetries<T>(fn: (attempt: number) => Promise<T>, opts?: { attempts?: number; baseMs?: number; maxMs?: number; isRetriable?: (err: unknown) => boolean; getRetryAfterMs?: (err: unknown) => number | null }): Promise<T>`
- Behavior:
  - For attempt 1..N: `try { return await fn(attempt) } catch (err) { if (!isRetriable(err) || attempt === attempts) throw; compute delayMs = getRetryAfterMs(err) ?? Math.min(maxMs, baseMs * 2 ** (attempt - 1)) + jitter; await sleep(delayMs); continue; }`
  - `isRetriable` default: 429 or 5xx via helper using `getStatusFromAIError` pattern.
  - `getRetryAfterMs` default: attempt to read `err.response?.headers?.get?.('retry-after')` (Fetch), `err.headers?.['retry-after']` (Axios-like), or `err.cause?.response?.headers`, parse seconds or HTTP-date.
  - Jitter: `Math.floor(Math.random() * 0.25 * baseDelay)`; clamp to `maxMs`.

2) Extend AgentConfig for retry parameters
- File: `src/main/agent/config.ts`
  - Add fields to `AgentConfig`:
    - `RETRY_ATTEMPTS: number; RETRY_BASE_MS: number; RETRY_MAX_MS: number;`
  - Defaults (env precedence):
    - `RETRY_ATTEMPTS = Number(process.env.PF_AGENT_RETRY_ATTEMPTS ?? 3)`
    - `RETRY_BASE_MS = Number(process.env.PF_AGENT_RETRY_BASE_MS ?? 800)`
    - `RETRY_MAX_MS = Number(process.env.PF_AGENT_RETRY_MAX_MS ?? 8000)`
  - Pref keys (optional):
    - `agent.retryAttempts`, `agent.retryBaseMs`, `agent.retryMaxMs` (read if present, fallback to env/defaults).

3) Wrap stream creation in retry (main path)
- File: `src/main/api-route-handlers.ts`
  - Import helper: `import { withRateLimitRetries } from './utils/retry';`
  - Compute `const start = Date.now();` before attempt.
  - Create an attempt function that builds the `streamText` result but does not pipe yet:
    - Example signature: `const createStream = async (attempt: number) => { return streamText({ model, system, messages: modelMessages, tools: disableTools ? undefined : tools, temperature: ..., maxOutputTokens: ..., abortSignal: controller.signal, onAbort: () => {}, onFinish /* telemetry hook from telemetry plan */ }); }`
  - Call: `const result = await withRateLimitRetries(createStream, { attempts: cfg.RETRY_ATTEMPTS, baseMs: cfg.RETRY_BASE_MS, maxMs: cfg.RETRY_MAX_MS, isRetriable: this.isRetriableProviderError.bind(this), getRetryAfterMs: this.getRetryAfterMsFromError.bind(this) });`
  - After obtaining `result`, pipe once:
    - `if (res.headersSent) return;` (safety; it shouldn’t be sent yet)
    - `result.pipeUIMessageStreamToResponse(res, { consumeSseStream: consumeStream });`

4) Wrap the toolless fallback creation path similarly
- Mirror the above for the fallback streamText call.

5) Add helper methods to `APIRouteHandlers` (or inline in retry util)
- `private isRetriableProviderError(err: unknown): boolean`:
  - Use `this.getStatusFromAIError(err)`; return true for 429, 500, 502, 503, 504; false otherwise.
- `private getRetryAfterMsFromError(err: unknown): number | null`:
  - Inspect likely header locations; parse numeric seconds or RFC-1123 date to ms; sanitize bounds.

6) Development logging
- On retry, log warning in dev: `console.warn('[AI] retrying after provider backoff', { attempt, delayMs })`.

## Function/Method Signatures
- New: `src/main/utils/retry.ts`
  - `export async function withRateLimitRetries<T>(fn: (attempt: number) => Promise<T>, opts?: { attempts?: number; baseMs?: number; maxMs?: number; isRetriable?: (err: unknown) => boolean; getRetryAfterMs?: (err: unknown) => number | null }): Promise<T>`
- Modified: `src/main/agent/config.ts` (AgentConfig augmentation + resolveAgentConfig fields).
- New (optional in `APIRouteHandlers`):
  - `private isRetriableProviderError(err: unknown): boolean`
  - `private getRetryAfterMsFromError(err: unknown): number | null`

## Database Schema Changes
- None.

## API Endpoint Changes
- Endpoint: Chat stream (`handleChat`)
- Request/Response: Unchanged.
- Behavioral change: silent pre-pipe retries on transient provider errors.

## Configuration Changes
- New env vars (with preferences fallback):
  - `PF_AGENT_RETRY_ATTEMPTS` (default 3)
  - `PF_AGENT_RETRY_BASE_MS` (default 800)
  - `PF_AGENT_RETRY_MAX_MS` (default 8000)
- Pref keys (optional): `agent.retryAttempts`, `agent.retryBaseMs`, `agent.retryMaxMs`.

## Dependencies
- No new NPM packages.
- New internal util module only.

## Error Handling & Edge Cases
- Never retry after `res.headersSent`.
- Respect `Retry-After` seconds or absolute-date values; clamp to max.
- Abort handling remains wired via `AbortController`.
- Do not retry tool-schema errors; existing toolless fallback remains separate logic.

## Backward Compatibility & Migration Strategy
- Fully backward compatible; no API surface changes.
- Retries are bounded and configurable; can be disabled by setting attempts to 1.

## Risk Assessment & Regression Prevention
- Affected areas: stream setup flow in `handleChat` (both tool and toolless paths).
- Tests to update/extend:
  - Existing: `src/main/__tests__/api-chat-retry-invalid-tool.test.ts` should continue to pass.
  - New: `src/main/__tests__/api-chat-retry-429.test.ts`:
    - Mock provider to throw 429 pre-stream; assert retry attempts and eventual success path pipes response.
    - Verify no retries when `res.headersSent` is true.
    - Verify 5xx transient errors are retried; 4xx non-429 are not.

## Implementation Order
1. Add retry util module with unit tests.
2. Extend AgentConfig with retry settings.
3. Integrate into `handleChat` (main path), then mirror in fallback path.
4. Run and fix tests; verify logs and behavior with simulated 429.

## Shared Components / Dependencies with Other Improvements
- Shared with telemetry plan: the same `handleChat` call site; ensure `onFinish` telemetry remains inside the stream options and unaffected by retries (telemetry runs only on the final, successful attempt).
- Independent of context tool changes.
