# Workspace‑Scoped Agent Chat History — Analysis and Implementation Plan

This document analyzes the current agent/chat architecture and outlines a detailed plan to implement workspace‑specific agent chat history with multi‑thread support and local JSON persistence.

Last updated: 2025‑09‑04

---

## Goals and Requirements

- Workspace‑scoped chat history: each workspace has its own agent chat threads and storage directory.
- Multiple chat threads: create new chats per workspace; not a single persistent thread.
- Thread management UI: list threads, open/resume a thread, delete threads; future‑friendly for rename/pin.
- Local persistence: store threads as JSON files on disk using a dedicated folder structure per workspace.

Non‑goals for this iteration
- Cloud sync or remote storage.
- Full token accounting or cost analytics (can be added later using the existing `usage_summary`).
- Cross‑workspace migrations or sharing.

---

## Current Architecture Overview (as‑is)

Key files reviewed
- Renderer: `src/components/agent-panel.tsx` (main chat UI), `src/components/model-settings-modal.tsx` (exports session), `src/handlers/electron-handlers.ts` (workspace helpers), hooks under `src/hooks/*`.
- Main: `src/main/api-server.ts` (Express routes), `src/main/api-route-handlers.ts` (chat route, workspace routes), `src/main/db/**/*` (SQLite persistence), `src/main/main.ts` (IPC wiring), `src/main/preload.ts` (renderer bridge), `src/main/file-service.ts` (FS guards), `src/main/agent/*` (agent tools, model resolver, config), `src/main/workspace-context.ts` (allowed paths).

Current chat flow
- Renderer (`AgentPanel`):
  - Uses `useChat` from `@ai-sdk/react` to post to `POST /api/v1/chat` with a custom fetch that injects `Authorization` and `X-Pasteflow-Session` headers.
  - On mount, creates a durable session ID via `ipcRenderer.invoke('agent:start-session')` and holds it in local state.
  - Tracks a single message list in memory (from `useChat`). No hydration from disk or DB.
  - Sends a context envelope (files, workspace path) per request via `prepareSendMessagesRequest`.
  - Does not support multiple threads yet; there’s one session seeded on mount.
- Server (`handleChat` in `src/main/api-route-handlers.ts`):
  - Validates body, converts UI → model messages, builds system prompt from envelope, streams via `ai.streamText` with tools.
  - On completion, persists the full message list (capped) into SQLite: `chat_sessions (id=sessionId, workspace_id, messages JSON)`.
  - Logs tool executions and usage summaries (`tool_executions`, `usage_summary`).
- IPC (main):
  - `agent:start-session` seeds an empty DB session and returns a sessionId.
  - `agent:get-history` fetches messages JSON for a `sessionId` from DB.
  - `agent:export-session` writes a combined payload (session + tools + usage) to a JSON file (default: Downloads).

Workspace model (renderer + main)
- Workspaces are managed in SQLite (tables: `workspaces`, `preferences`).
- Active workspace is tracked via pref `workspace.active` and `setAllowedWorkspacePaths([folder])`, which guards file access.
- Renderer has helpers to create/activate workspaces on folder selection and to listen to `workspace-updated` events.

Local FS access patterns
- File reads/writes for user files are guarded by `validateAndResolvePath` and the allowed workspace roots.
- App‑owned persistence (DB, session export) uses Electron’s `app.getPath('userData')` or safe destinations like Downloads.
- Preload exposes a permissive `ipcRenderer.invoke` bridge; channels are validated in main via zod schemas.

Gaps vs requirements
- No concept of multiple chat threads per workspace in the renderer.
- No thread list UI; cannot resume or delete.
- Chat history is only in SQLite; not persisted per‑workspace as JSON files.
- No IPC or API to list sessions by workspace or to manage JSON threads.

---

## Target Architecture (to‑be)

High‑level
- Introduce a file‑based thread store under the app’s user data directory, partitioned by workspace.
- Keep DB session logging for telemetry, but use JSON files as the source of truth for thread content and metadata.
- Expose IPC for thread CRUD. Keep HTTP chat route unchanged.
- Update the Agent Panel to manage and switch `sessionId` (chat threads) and hydrate messages from the selected thread.

Storage layout
- Root: `<userData>/.agent-threads/`
- Per workspace directory (by stable identifier): `<userData>/.agent-threads/<workspaceKey>/`
  - Workspace key: preferred `workspaceId` (string from DB); fallback to `sha1(folderPath)` if needed.
- Per thread file: `<workspaceDir>/thread-<sessionId>.json`
- Optional per workspace index: `<workspaceDir>/index.json` to speed up listing (can also scan files).

Thread file schema (v1)
```jsonc
{
  "version": 1,
  "sessionId": "uuid-or-random",
  "workspace": { "id": "string", "name": "string", "folderPath": "string" },
  "meta": {
    "title": "First line of first user message or custom title",
    "createdAt": 1736037900000,
    "updatedAt": 1736037955000,
    "model": "gpt-4o-mini",
    "provider": "openai",
    "messageCount": 12
  },
  "messages": [ /* `@ai-sdk/react` UI message objects */ ],
  // Optional (future): include tool executions and usage snapshots to avoid DB coupling
  "toolExecutions": [],
  "usage": []
}
```

IPC surface (renderer → main)
- `agent:threads:list` — List threads for the active or specified workspace.
  - Input: `{ workspaceId?: string }` (if omitted: active workspace)
  - Output: `{ threads: Array<{ sessionId, title, createdAt, updatedAt, messageCount, filePath }> }`, sorted by `updatedAt` desc.
- `agent:threads:load` — Load a thread’s full JSON by `sessionId`.
  - Input: `{ sessionId: string }`
  - Output: Thread JSON file payload (above schema)
- `agent:threads:saveSnapshot` — Create/update a thread file from the current message list.
  - Input: `{ sessionId: string, workspaceId?: string, messages: UIMessage[], meta?: Partial<meta> }`
  - Output: `{ ok: true, filePath, created: boolean }`
- `agent:threads:delete` — Delete a thread file (and optionally prune DB rows).
  - Input: `{ sessionId: string }`
  - Output: `{ ok: true }`
- `agent:threads:rename` (optional) — Rename thread title only.
  - Input: `{ sessionId: string, title: string }`
  - Output: `{ ok: true }`

Notes
- Validate inputs with zod in `src/main/ipc/schemas.ts`.
- All file operations must be confined under `<userData>/.agent-threads/` — do NOT call `validateAndResolvePath` (that API is for workspace files), but do sanitize names and ensure the computed path is within the root.
- Use `fs.promises.mkdir({ recursive: true })` and atomic writes (`writeFile` to temp then rename) to avoid corruption.

Renderer integration
- AgentPanel gains thread awareness:
  - New controls in the header: “New Chat” and a threads menu/list button.
  - Do NOT auto‑create a session on mount. On mount:
    - Fetch `agent:threads:list` for the active workspace.
    - Read `/prefs/get` key `agent.lastSession.<workspaceId>`; if present and exists, open it; else open the most recently updated thread; if none, show an empty state prompting “New Chat”.
  - Manage current `sessionId` in state and key `useChat` by `sessionId` to remount for new/loaded threads.
  - On “New Chat”: call `agent:start-session` → set new `sessionId` → clear composer → persist empty thread file (createdAt) via `agent:threads:saveSnapshot` → store `/prefs/set` for `agent.lastSession.<workspaceId>`.
  - On “Open Thread”: first cancel any in‑flight stream (`stop()`/interrupt), then call `agent:threads:load(sessionId)` → hydrate initial messages into `useChat` → store `/prefs/set` for `agent.lastSession.<workspaceId>`.
    - If `useChat` supports `initialMessages`, pass it; otherwise, store in local state and render from that until the first send remounts the hook.
  - On each successful turn (`onFinish`): call `agent:threads:saveSnapshot` with current `messages` to update JSON file (and title/messageCount/updatedAt).
  - On delete: first ensure streaming is cancelled via `stop()`/interrupt; if deleting current thread, resolve to the next most recent or create a new chat.
- Thread list UI:
  - New component `src/components/agent-thread-list.tsx`: fetches `agent:threads:list`, shows items with title, time, and actions (open/delete).
  - Optionally add a compact dropdown in `agent-panel-header`. A larger modal is acceptable for first pass.

DB interplay
- JSON is the single source of truth for thread bodies and metadata.
- `chat_sessions` may continue to exist for telemetry or can be pruned later; the UI does not read from it.
- Deleting a thread can optionally clean up DB `chat_sessions` rows; `tool_executions` and `usage_summary` may be retained until a retention policy is defined.

Security and safety
- JSON thread files live under `<userData>/.agent-threads/` and are not exposed to the agent tools (which are workspace‑scoped).
- No arbitrary paths accepted from renderer; main composes all storage paths.
- Add size and count caps:
  - Max JSON file size (e.g., 10 MB) and max messages per thread (e.g., 100 or configurable with `PF_AGENT_MAX_SESSION_MESSAGES`).
  - Prune old threads per workspace (optional follow‑up) to the last N files.

---

## Data Flow Mapping (current → target)

Current (single session)
1) `AgentPanel` mounts → `agent:start-session` → `sessionId` in state.
2) User sends message → `useChat` posts to `/api/v1/chat` with context and `X-Pasteflow-Session`.
3) Server streams, then persists full message list to DB → DB row reflects latest state.
4) Renderer displays messages from `useChat` only (no hydration across reloads).

Target (multi‑thread, per workspace)
1) AgentPanel loads → fetch `threads:list` for active workspace → render list.
2) “New Chat” → new `sessionId` (IPC) → create JSON file (snapshot with 0 messages).
3) “Open” → load thread JSON → hydrate messages into `useChat` (or local state → remount hook keyed by sessionId).
4) Send message → server streams; on `onFinish` → `threads:saveSnapshot` with updated messages/metadata.
5) Delete thread → `threads:delete` removes JSON (and optionally DB row) → UI updates list + current thread fallback.

---

## Implementation Plan

Phase 1 — Storage + IPC (main process)
1) Add `src/main/agent/chat-storage.ts` (new):
   - Helpers: `getThreadsRoot()`, `getWorkspaceKey(ws)`, `getWorkspaceDir(ws)`, `safeWriteJsonAtomic(file, data)`, `loadThread(sessionId)`, `listThreads(workspaceId)`, `saveSnapshot({ sessionId, ws, messages, meta })`, `deleteThread(sessionId)`, `renameThread(sessionId, title)`.
   - Use Electron `app.getPath('userData')` for root. Ensure all paths remain inside the root (defense‑in‑depth).
2) Extend IPC schemas (`src/main/ipc/schemas.ts`):
   - `AgentThreadsListSchema`, `AgentThreadsLoadSchema`, `AgentThreadsSaveSnapshotSchema`, `AgentThreadsDeleteSchema`, `AgentThreadsRenameSchema`.
3) Wire IPC in `src/main/main.ts`:
   - Handle `agent:threads:list|load|saveSnapshot|delete|rename` using storage helpers.
   - No runtime fallback to DB for thread bodies; threads must exist in JSON.

Phase 2 — Renderer UI and state
4) Add `src/components/agent-thread-list.tsx` (list + actions) and minimal styles.
5) Update `src/components/agent-panel.tsx`:
   - Header: add “New Chat” and “Threads” button; remove automatic `agent:start-session` on mount.
   - Maintain `sessionId` state with the current value (already present) and a `key={sessionId}` on the chat body to remount.
   - Load: when selecting a thread, call `agent:threads:load`, seed `useChat` via `initialMessages` (if available) or controlled local state and reset composer/interruptions.
   - Save: on `onFinish`, call `agent:threads:saveSnapshot` with `{ sessionId, messages, meta: { model, provider } }` and update the local list if open.
   - Delete: call `agent:threads:delete`; if deleting current thread, choose another thread or create a new chat.
   - Persist last opened session per workspace via `/prefs/set` key `agent.lastSession.<workspaceId>`; read it on mount to restore.

Phase 3 — Polish + edge cases
6) Titles: default to first user message line; add `rename` from the menu.
7) Empty states: friendly states for no workspace, no threads, or missing JSON.
8) Performance: lazy load thread bodies; list returns metadata only.
9) Limits: enforce max messages/file size and prune in save path.
10) Optional: list threads by `updatedAt` with pagination (if many files).

Testing
- Unit tests (main):
  - Chat storage helpers: path computation, atomic writes, list/load/delete.
  - IPC handlers: zod validation and happy‑path operations via temporary directories.
- Integration tests (main server optional): ensure `/api/v1/chat` remains unchanged.
- Renderer tests:
  - Agent panel thread switching logic (using JSDOM + mocks for IPC).
  - Thread list component behaviors (open/delete/new) and state updates.

Telemetry and migration
- No runtime migration. Legacy DB‑only sessions are not loaded by the UI.
- If needed, provide a one‑time migration utility to export DB sessions to JSON (out of scope for this plan).
- Retain DB data for tools/usage; JSON schema reserves `toolExecutions`/`usage` fields for potential future consolidation.

---

## Integration Points and File Changes (surgical)

Main process
- New: `src/main/agent/chat-storage.ts` (storage helpers).
- Update: `src/main/ipc/schemas.ts` (agent threads schemas).
- Update: `src/main/main.ts` (IPC handlers for agent threads CRUD).

Renderer
- New: `src/components/agent-thread-list.tsx` (+ CSS if needed).
- Update: `src/components/agent-panel.tsx` (header controls, session switching, load/save hooks).

No changes required to `POST /api/v1/chat` route or tool execution pipelines.

---

## Data Models (TS)

```ts
// Metadata returned in list
type AgentThreadListItem = {
  sessionId: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
  filePath: string; // absolute path in userData (not exposed to tools)
};

// Thread file shape
type AgentThreadFileV1 = {
  version: 1;
  sessionId: string;
  workspace: { id: string; name: string; folderPath: string };
  meta: { title: string; createdAt: number; updatedAt: number; model?: string; provider?: string; messageCount: number };
  messages: any[]; // @ai-sdk/react UI messages
  toolExecutions?: any[];
  usage?: any[];
};
```

---

## Risks and Mitigations

- Divergence between DB and JSON
  - Mitigation: Update JSON on each `onFinish`. The UI does not read from DB; minor telemetry drift is acceptable until a dedicated reconciliation/pruning job is added.
- Large message arrays inflating JSON files
  - Mitigation: cap messages per thread (env var already used in server), optionally compress or store deltas in the future.
- Thread hydration with `useChat`
  - Mitigation: key component by `sessionId` to remount the hook; fall back to local display state until first send.
- Cross‑platform paths and permissions
  - Mitigation: always anchor to `app.getPath('userData')`; sanitize and bound‑check all computed paths.
 - Unintended empty sessions
   - Mitigation: Remove auto session creation on mount; only create sessions when the user clicks “New Chat” or when explicitly needed.
 - Listing performance for large workspaces
   - Mitigation: For now, parse each thread JSON’s `meta` for listing; if slow, add and maintain a compact `index.json` per workspace for O(1) listings.

---

## Open Questions

- Should delete also remove DB telemetry (tool executions and usage)? For now, likely keep telemetry; optionally flag as archived.
- Do we want a per‑thread model/provider override stored with the thread meta? This plan leaves hooks for it.
- Should threads also store a lightweight “context envelope snapshot” for debugging? Optional field can be added later.

---

## Rollout Strategy

- Immediate rollout with the new thread system.
- Remove legacy single‑thread behavior and any dead code paths.
- No feature flags and no backward compatibility maintenance.

---

## Summary

This plan adds a minimal, robust multi‑thread chat system per workspace with local JSON persistence under the app’s user data directory, while preserving existing DB telemetry. It introduces precise IPC endpoints, a small storage module in main, and modest UI enhancements to `AgentPanel` for creating, listing, opening, and deleting threads. Security boundaries remain intact: agent tools cannot access the JSON store, and all user‑file operations continue to be workspace‑guarded.
