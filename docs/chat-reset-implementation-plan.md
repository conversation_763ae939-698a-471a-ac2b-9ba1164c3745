# Chat Reset on Workspace Change — Analysis and Implementation Plan

Last updated: 2025-09-06

Goal: Ensure that whenever a user switches to a different workspace or loads a new workspace, the Agent Panel starts a fresh chat thread automatically rather than continuing the previous conversation.

---

## 1) Current implementation analysis

### Where chat threads live and how they’re stored
- Threads are persisted per workspace under the app’s userData directory in JSON files.
- Workspace key is derived from workspace.id if available, else a hash of folderPath/name.

```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/agent/chat-storage.ts start=47
export function getWorkspaceKey(ws: WorkspaceRef): string {
  const raw = (ws && typeof ws.id === "string" && ws.id.trim().length > 0)
    ? String(ws.id)
    : safeSha1(ws.folderPath || ws.name || randomUUID());
  // Make filename/dir-safe: allow alnum, dash, underscore
  return raw.replace(/[^a-zA-Z0-9_-]/g, "-");
}
```

- <PERSON><PERSON><PERSON> writes use the workspace provided (workspace.id/name/folderPath) and clamp message count; files are only written under the app's userData directory (no workspace mirror).

```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/agent/chat-storage.ts start=247
const toWrite: AgentThreadFileV1 = {
  version: 1,
  sessionId,
  workspace: { id: workspace.id, name: workspace.name, folderPath: workspace.folderPath },
  meta: {
    title,
    createdAt,
    updatedAt: now,
    model,
    provider,
    messageCount,
  },
  messages: truncated,
  toolExecutions: existing?.toolExecutions,
  usage: existing?.usage,
};
await safeWriteJsonAtomic(fp, toWrite);
```

- Loading a thread by sessionId scans all workspace dirs, i.e., it is not scoped purely by current workspace.

```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/agent/chat-storage.ts start=121
export async function loadThread(sessionId: string): Promise<AgentThreadFileV1 | null> {
  const root = getThreadsRoot();
  // Scan workspace dirs for the file
  let target: string | null = null;
  try {
    const entries = await fs.promises.readdir(root, { withFileTypes: true });
    for (const dirent of entries) {
      if (!dirent.isDirectory()) continue;
      const fp = path.join(root, dirent.name, `thread-${sessionId}.json`);
      if (isWithinRoot(fp, root)) {
        try {
          await fs.promises.access(fp, fs.constants.R_OK);
          target = fp; break;
        } catch {
          // not found in this workspace
        }
      }
    }
  } catch {
    // root may not exist yet
  }
  if (!target) return null;
  const raw = await fs.promises.readFile(target, "utf8");
  const parsed = JSON.parse(raw);
  return parsed as AgentThreadFileV1;
}
```

### Agent thread IPC

```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/main.ts start=1109
ipcMain.handle('agent:threads:list', async (_e, params: unknown) => {
  // resolves workspaceId (from param or preference 'workspace.active'), then calls listThreads(ws)
});
ipcMain.handle('agent:threads:load', async (_e, params: unknown) => {
  // loadThread(sessionId) — global scan across workspaces
});
ipcMain.handle('agent:threads:saveSnapshot', async (_e, params: unknown) => {
  // resolves workspaceId (from param or preference), then saveSnapshot({ sessionId, workspace, messages, meta })
});
```

### How the Agent Panel detects and reacts to workspace state
- The panel derives panelEnabled from currentWorkspace and selectedFolder props.
- It resolves the active workspace id from the DB preference `workspace.active` and listens to updates.

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx start=119
const refreshActiveWorkspace = useCallback(async () => {
  try {
    const res = await (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'workspace.active' });
    const parsed = res as PrefsGetResponse<string> | undefined;
    const wsId = parsed && parsed.success && typeof parsed.data === 'string' ? parsed.data : null;
    setActiveWorkspaceId(wsId || null);
  } catch {
    setActiveWorkspaceId(null);
  }
}, []);
```

- It also subscribes to app-level events to keep in sync.

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx start=184
useEffect(() => {
  const onWsLoaded = () => { void refreshActiveWorkspace(); };
  const onDirectOpen = () => { void refreshActiveWorkspace(); };
  window.addEventListener('workspaceLoaded', onWsLoaded as EventListener);
  window.addEventListener('directFolderOpened', onDirectOpen as EventListener);
  let prefUpdateHandler: ((...args: unknown[]) => void) | null = null;
  try {
    const ipc = (window as any).electron?.ipcRenderer;
    if (ipc?.on) {
      prefUpdateHandler = () => { void refreshActiveWorkspace(); };
      ipc.on('/prefs/get:update', prefUpdateHandler);
    }
  } catch {}
  return () => {
    window.removeEventListener('workspaceLoaded', onWsLoaded as EventListener);
    window.removeEventListener('directFolderOpened', onDirectOpen as EventListener);
    try {
      const ipc = (window as any).electron?.ipcRenderer;
      if (ipc?.removeListener && prefUpdateHandler) ipc.removeListener('/prefs/get:update', prefUpdateHandler);
    } catch {}
  };
}, [refreshActiveWorkspace]);
```

- When selectedFolder becomes falsy, the panel resets its local chat state; otherwise, it does not clear on workspace change.

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx start=163
useEffect(() => {
  let cancelled = false;
  // If folder is cleared, disable panel immediately
  if (!selectedFolder) {
    setActiveWorkspaceId(null);
    setSessionId(null);
    setHydratedMessages([]);
    return () => { cancelled = true; };
  }
  void refreshActiveWorkspace();
  return () => { cancelled = true; };
}, [selectedFolder, refreshActiveWorkspace]);
```

### Chat creation and bootstrapping behavior
- Auto-initialize a new chat session only when panelEnabled is true and no session exists.

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx start=235
useEffect(() => {
  if (!panelEnabled) {
    hasAutoInitializedRef.current = false;
    autoInitAttemptsRef.current = 0;
    return;
  }
  if (sessionId) return; // already active
  if (hasAutoInitializedRef.current) return; // guard
  (async () => {
    const id = await ensureSession();
    if (id) {
      hasAutoInitializedRef.current = true;
      autoInitAttemptsRef.current = 0;
    } else {
      // retry…
    }
  })();
}, [panelEnabled, sessionId, ensureSession]);
```

- Additionally, when the active workspace id changes and there is no active session (and not streaming/submitted), the panel tries to bootstrap the most recent thread for that workspace (or the lastSession preference), effectively restoring prior chat threads.

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx start=610
useEffect(() => {
  let cancelled = false;
  const bootstrapThreadsForWorkspace = async (wsId: string | null) => {
    if (!wsId) return;
    // Do not change threads while a session is active or a message is streaming
    if (sessionId || status === 'streaming' || status === 'submitted') return;
    try {
      const listRaw = await (window as any).electron?.ipcRenderer?.invoke?.('agent:threads:list', { workspaceId: wsId });
      // choose target session: lastSession.<wsId> or most recent thread
      // then load and hydrate
    } catch {
      // ignore
    }
  };
  void bootstrapThreadsForWorkspace(activeWorkspaceId);
  return () => { cancelled = true; };
}, [activeWorkspaceId, sessionId, status]);
```

- On each chat `onFinish`, the panel persists a snapshot with the resolved workspace id; preference `agent.lastSession.<wsId>` is updated.

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/agent-panel.tsx start=331
onFinish: async (finishInfo: any) => {
  if (sessionId) {
    const [p, m] = await Promise.all([
      (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.provider' }),
      (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.defaultModel' }),
    ]);
    for (let attempt = 0; attempt < 5; attempt++) {
      try {
        const wsId = await resolveWorkspaceId();
        const res = await (window as any).electron?.ipcRenderer?.invoke?.('agent:threads:saveSnapshot', {
          sessionId,
          workspaceId: wsId || undefined,
          messages: (finishInfo && finishInfo.messages) ? finishInfo.messages : undefined,
          meta: { model, provider },
        });
        // then set agent.lastSession.<wsId>
      } catch {
        // retry
      }
    }
  }
}
```

### How workspace loading/switching is detected
- The main server sets `workspace.active` and broadcasts updates when loading a workspace via the HTTP API, and electron handlers dispatch browser events for UI-only paths.

```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts start=684
async handleLoadWorkspace(req: Request, res: Response) {
  // setPreference('workspace.active', id)
  // setAllowedWorkspacePaths and validators
  broadcastToRenderers('folder-selected', ws.folder_path);
  broadcastWorkspaceUpdated({ workspaceId: String(ws.id), folderPath: ws.folder_path, selectedFiles: [...] });
}
```

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/workspace-modal.tsx start=324
if (workspaceData) {
  window.dispatchEvent(new CustomEvent('workspaceLoaded', { detail: { name: wsName, workspace: workspaceData } }));
  onClose();
}
```

```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/workspace-dropdown.tsx start=103
if (workspaceData) {
  window.dispatchEvent(new CustomEvent('workspaceLoaded', { detail: { name, workspace: workspaceData } }));
}
```

```ts path=/Users/<USER>/Documents/development/pasteflow/src/handlers/electron-handlers.ts start=258
window.dispatchEvent(new CustomEvent('directFolderOpened', {
  detail: { name: workspaceName, workspace: minimalWorkspaceData }
}));
```

---

## 2) Problem identification

- Chat threads currently persist across workspace changes because the Agent Panel does not clear its `sessionId` or message state when `activeWorkspaceId` changes; it only clears when `selectedFolder` becomes `null`.
- The bootstrapping effect restores the last thread for the new workspace only if there is no active session. Since `sessionId` typically remains set, no reset occurs, and the previous conversation continues in the new workspace.
- Loading a thread by `sessionId` is global (scans all workspaces), so an existing `sessionId` can accidentally refer to a thread file from a different workspace.
- If we reuse `handleNewChat` to transition between workspaces, it saves the previous session snapshot using `resolveWorkspaceId()` which returns the current (new) workspace id; that would write the previous session into the new workspace unless we override the target workspace id.

Conclusion: There is no explicit “workspace changed → start fresh thread” behavior. The state is preserved inadvertently, and the bootstrapping logic opposes the desired reset.

---

## 3) Solution design

Desired behavior
- Whenever the active workspace changes (loading a saved workspace or opening a folder), the Agent Panel should:
  1) Stop any in-flight streaming turn.
  2) Save a final snapshot of the previous session to the previous workspace (best-effort).
  3) Clear the current chat state.
  4) Immediately create a brand new chat session bound to the new workspace.
  5) Optionally create an empty snapshot for the new thread (so it appears in the threads list), and set `agent.lastSession.<newWsId>` to the new id.
  6) Do not auto-restore the last session for the new workspace.

Trigger conditions
- Any change in the resolved `activeWorkspaceId` (from `/prefs/get workspace.active` updates) as observed in the Agent Panel.
- UI events for workspace switching/loading:
  - `workspaceLoaded` (loading from modal or dropdown)
  - `directFolderOpened` (open folder via OS dialog)

Detection and ordering
- Observe `activeWorkspaceId` and keep a `prevActiveWorkspaceIdRef` to detect transitions.
- When a transition is detected:
  - Stop streaming (`interruptNow()` which calls `stop()`; preserves a user-interrupted marker if needed).
  - Best-effort snapshot of the current session with `workspaceId: prevActiveWorkspaceIdRef.current` (override; do not use `resolveWorkspaceId()`).
  - Reset local state (sessionId, hydratedMessages, usage rows, interruptions, attachments, composer).
  - Start a new session via `agent:start-session`, set `sessionId`, and (optionally) immediately call `agent:threads:saveSnapshot` with `messages: []` and `workspaceId: newActiveWorkspaceId` to seed the thread file; set `/prefs/set agent.lastSession.<newWsId>`.
- Disable the current “bootstrap last thread” logic so it cannot race and reopen a previous thread.

Preserve UI affordances
- The user can still open older threads manually using the Threads list. We are only changing the default behavior on workspace switches.

---

## 4) Implementation plan

Files to modify
- src/components/agent-panel.tsx

Changes in AgentPanel
1) Add refs/state to track workspace transitions and cancel races:
   - `const prevWorkspaceIdRef = useRef<string | null>(null);`
   - `const switchTokenRef = useRef<number>(0); // increment on each workspace change`
   - Optional: `const disableBootstrapRef = useRef<string | null>(null);` to suppress bootstrap on the next change.

2) Add an effect that reacts to `activeWorkspaceId` changes and enforces a fresh thread:

```tsx path=null start=null
useEffect(() => {
  const newWs = activeWorkspaceId;
  const prevWs = prevWorkspaceIdRef.current;
  if (!newWs || newWs === prevWs) {
    prevWorkspaceIdRef.current = newWs;
    return;
  }

  prevWorkspaceIdRef.current = newWs;
  disableBootstrapRef.current = newWs; // guard against bootstrap race
  const token = Date.now();
  switchTokenRef.current = token;

  (async () => {
    try {
      // 1) Stop any in-flight turn
      try { interruptNow(); } catch {}

      // 2) Best-effort snapshot of previous session to previous workspace
      if (sessionId && prevWs) {
        try {
          const [p, m] = await Promise.all([
            (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.provider' }),
            (window as any).electron?.ipcRenderer?.invoke?.('/prefs/get', { key: 'agent.defaultModel' }),
          ]);
          const provider = (p && p.success && typeof p.data === 'string') ? p.data : undefined;
          const model = (m && m.success && typeof m.data === 'string') ? m.data : undefined;
          await (window as any).electron?.ipcRenderer?.invoke?.('agent:threads:saveSnapshot', {
            sessionId,
            workspaceId: prevWs, // IMPORTANT: use previous workspace id explicitly
            messages,
            meta: { model, provider },
          });
        } catch {}
      }

      // 3) Clear local state (prevent races with bootstrap)
      setIsSwitchingThread(true);
      setAwaitingBind(true);
      setSessionId(null);
      setHydratedMessages([]);
      setPendingAttachments(new Map());
      setComposer("");
      setUsageRows([]);
      setLastUsage(null);
      setInterruptions(new Map());

      // 4) Start a brand new session for the new workspace
      const res: any = await (window as any).electron?.ipcRenderer?.invoke?.('agent:start-session', {});
      const id = (res && res.success) ? res.data?.sessionId : (res?.data?.sessionId || res?.sessionId);
      if (!id || switchTokenRef.current !== token) return; // abort if switched again
      setSessionId(id);
      setHydratedMessages([]);

      // 5) Seed lastSession + (optional) empty snapshot
      try { await (window as any).electron?.ipcRenderer?.invoke?.('/prefs/set', { key: `agent.lastSession.${newWs}`, value: id }); } catch {}
      try { await (window as any).electron?.ipcRenderer?.invoke?.('agent:threads:saveSnapshot', { sessionId: id, workspaceId: newWs, messages: [], meta: {} }); } catch {}
    } finally {
      if (switchTokenRef.current === token) {
        setIsSwitchingThread(false);
        setAwaitingBind(false);
      }
    }
  })();
}, [activeWorkspaceId]);
```

3) Remove or disable the existing “bootstrap last thread” effect. If you want to keep the code for future use, gate it like this:

```tsx path=null start=null
useEffect(() => {
  if (!activeWorkspaceId) return;
  // Skip bootstrapping if we just switched workspaces and created a fresh thread
  if (disableBootstrapRef.current === activeWorkspaceId) return;
  if (sessionId || status === 'streaming' || status === 'submitted') return;
  // Otherwise (if ever enabled), this would list+load threads
}, [activeWorkspaceId, sessionId, status]);
```

4) Keep the existing auto-init effect as a fallback. In practice the new effect pre-creates the fresh session, so auto-init won’t run, but it helps in DB readiness/race scenarios.

Notes
- Do NOT call the existing `handleNewChat` for workspace switches, because it resolves the workspace id dynamically and would snapshot the previous session under the new workspace. The new effect explicitly targets the previous workspace id for the final snapshot.
- You may optionally move some of the snapshot logic into a small helper to share with `onFinish` and avoid code duplication.

Edge cases handled
- Rapid switching: `switchTokenRef` ensures only the latest switch completes; previous tasks bail out.
- Streaming turn: `interruptNow()` sets a marker and calls `stop()`.
- Preference update races: `awaitingBind` and `isSwitchingThread` prevent the input from enabling mid-transaction; bootstrapping is disabled during the switch.
- DB or IPC errors: All IPC calls are best-effort with local state resets still proceeding to guarantee a fresh thread UX.

---

## 5) Testing plan

Add a new test suite under `src/__tests__/`:

- agent-panel-workspace-switch-reset.test.tsx
  - “resets to a new, empty thread when workspaceLoaded is dispatched”
    - Dispatch `workspaceLoaded` with a different workspace; assert:
      - `agent:start-session` is invoked once.
      - `agent:threads:saveSnapshot` is invoked with the previous sessionId and previous workspace id.
      - `sessionId` changes, messages render empty state.
  - “stops streaming and resets when switching workspaces mid-stream”
    - Mock `useChat` status as `streaming`; dispatch workspace change; assert `stop()` called and new session created.
  - “does not restore last thread after workspace switch”
    - Seed `agent.lastSession.<wsId>`; ensure bootstrapping is skipped and a new session is created instead.
  - “rapid consecutive workspace switches only create the final session”
    - Dispatch two `workspaceLoaded` events in quick succession; assert only final ws created a session and intermediate attempt bailed.

Manual QA
- Switch between saved workspaces via the dropdown and modal; verify:
  - The chat panel shows an empty conversation with a new session id each time.
  - No previous messages bleed into the new workspace.
  - Thread list still shows past threads and can open them manually.
- Open a folder directly; verify a new empty chat is created for that workspace.

---

## 6) Summary of exact changes

- src/components/agent-panel.tsx
  - Add: refs (`prevWorkspaceIdRef`, `switchTokenRef`, `disableBootstrapRef`).
  - Add: new `useEffect` reacting to `activeWorkspaceId` changes that interrupts, snapshots previous session to previous ws, clears state, and starts a new session for the new ws (plus optional empty snapshot + lastSession pref).
  - Change: disable or guard the existing bootstrap effect so it never restores the last thread on workspace switches.
  - Keep: existing auto-init effect as a safety net.

No changes required in main process or thread storage for this behavior.

---

## Appendix: Relevant code references

- Workspace activation and broadcasting
```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/api-route-handlers.ts start=684
async handleLoadWorkspace(req: Request, res: Response) {
  await this.db.setPreference('workspace.active', params.data.id);
  // broadcasts to renderers
}
```

- UI events for workspace changes
```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/workspace-modal.tsx start=324
window.dispatchEvent(new CustomEvent('workspaceLoaded', { detail: { name: wsName, workspace: workspaceData } }));
```
```tsx path=/Users/<USER>/Documents/development/pasteflow/src/components/workspace-dropdown.tsx start=103
window.dispatchEvent(new CustomEvent('workspaceLoaded', { detail: { name, workspace: workspaceData } }));
```
```ts path=/Users/<USER>/Documents/development/pasteflow/src/handlers/electron-handlers.ts start=258
window.dispatchEvent(new CustomEvent('directFolderOpened', { detail: { name: workspaceName, workspace: minimalWorkspaceData } }));
```

- Agent threads storage and IPC
```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/agent/chat-storage.ts start=47
export function getWorkspaceKey(ws: WorkspaceRef): string { /* … */ }
```
```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/agent/chat-storage.ts start=247
export async function saveSnapshot(/* … */) { /* … */ }
```
```ts path=/Users/<USER>/Documents/development/pasteflow/src/main/main.ts start=1109
ipcMain.handle('agent:threads:list', /* … */);
ipcMain.handle('agent:threads:load', /* … */);
icpMain.handle('agent:threads:saveSnapshot', /* … */);
```

By making these focused changes in the Agent Panel, we will reliably start a brand new chat thread on every workspace switch or load, while preserving the ability to access older threads manually via the thread list.
