# Implementation Plan: Deferred Agent Features (Terminal tool/UI, edit.block/multi, MiniFileList)

Date: 2025-09-07
Owner: PasteFlow
Related plan: docs/vercel-ai-sdk-agent-integration.md

## Context And Current State

Verified in codebase (paths are exact):
- Chat endpoint: Vercel AI SDK v5 streaming integrated
  - `src/main/api-route-handlers.ts` → `handleChat(...)` uses `streamText`, `convertToModelMessages`, and `consumeStream` to pipe UI Message Stream responses.
  - Retries and usage persistence implemented; falls back when tools unavailable.
- Agent tools (Phase 3 baseline)
  - `src/main/agent/tools.ts` exposes `file`, `search` (ripgrep-backed), `edit` (unified diff preview/apply, apply gated), `context` (summary/expand/search), and `terminal` (stub).
  - `src/main/tools/ripgrep.ts` implements ripgrep JSON search with caps + tests under `src/main/__tests__/ripgrep-runner.test.ts`.
- Security + config: `src/main/agent/security-manager.ts` (rate limiting, path validation), `src/main/agent/config.ts` (ENABLE_FILE_WRITE, ENABLE_CODE_EXECUTION, APPROVAL_MODE).
- UI: Agent Panel (renderer)
  - `src/components/agent-panel.tsx` with `@ai-sdk/react` `useChat`, session threads, token usage chip, composer, basic attachments (`AgentAttachmentList`).
  - IPC for threads/usage exists in `src/main/main.ts` (`agent:*` channels). No terminal IPC yet.

Deferred/not implemented:
- Terminal tool: tool is stubbed (`tools.ts`); no `node-pty`, no `xterm`, no `TerminalManager`, no `terminal:*` IPC.
- edit.block/edit.multi: only unified diff path exists; no block/multi variants.
- MiniFileList: no compact mini browser; Agent Panel shows pending attachments but no searchable, token-aware mini list.


## Goals

Deliver three features aligned with current patterns (rate-limited tools, preview-first, workspace-constrained security):
- Terminal tool/UI: safe session management in main (node-pty) with `xterm` renderer panel; agent calls are gated by config and approval.
- edit.block/multi: targeted replacements and multi-file operations with strong previews and conservative apply gates.
- MiniFileList: compact, searchable file list integrated in Agent Panel with token counters and lazy precision.


## Architecture Overview

Maintain the consolidated tools design (file, search, edit, terminal, context) and reuse existing services.

```
Renderer (Agent Panel) ── IPC ── Main (Tools + DB)
  │                              │
  │  useChat streams             │  tools: file | search | edit | context | terminal
  │  + MiniFileList              │  security: AgentSecurityManager + PathValidator
  │  + TerminalPanel (xterm)     │  token counting: getMainTokenService()
```


## Type Safety Strategy (Compliance With TYPESCRIPT.md)

- No any, no widening, strict flags honored. All added code paths assume `strict`, `noImplicitAny`, `strictNullChecks`, `noUncheckedIndexedAccess`, `exactOptionalPropertyTypes` on.
- Boundary validation + narrowing for every new entry point (IPC, tool inputs, HTTP), then operate on branded/internal types.
- Branded domain types (documented for consistent use across features):
  - `type Brand<T, B extends string> = T & { readonly __brand: B }` (doc helper)
  - `type SessionId = Brand<string, "SessionId">`
  - `type TerminalSessionId = Brand<string, "TerminalSessionId">`
  - `type WorkspaceId = Brand<string, "WorkspaceId">`
  - `type PathAbsolute = Brand<string, "PathAbsolute">`
  - `type Cursor = Brand<number, "Cursor">`; `type Bytes = Brand<number, "Bytes">`
- Discriminated unions for results and actions, with exhaustive `switch` and `never` assertions.
  - Result pattern: `type Ok<T> = { ok: true; value: T }`; `type Err<E extends string> = { ok: false; error: E }`; `type Result<T,E extends string> = Ok<T>|Err<E>`.
- Literal preservation: action enums, occurrence policies, channel names, and constants must use `as const` and/or `satisfies` to prevent widening.
- Schema-driven types: derive TypeScript types via `z.infer<typeof Schema>` and use type-only exports/imports for handlers and preload APIs.
- Exported functions: explicit param/return types; internal inference allowed only where it preserves precision.
- Error handling: catch as `unknown`, narrow with `instanceof Error` or safe `String(e)`; never cast `unknown` to `any`.


## Codebase Cross‑Reference (by feature)

Terminal
- Add: `src/main/terminal/terminal-manager.ts` (new), `src/components/terminal-panel.tsx` (new).
- Update: `src/main/agent/tools.ts` (implement real terminal actions), `src/main/main.ts` (terminal IPC), `src/main/ipc/schemas.ts` (terminal IPC types), `build/scripts/fix-dependencies.js` (asarUnpack `node-pty`).
- Packaging: ensure `node-pty` is unpacked; cross-platform shell resolution.

edit.block/multi
- Update: `src/main/agent/tools.ts` (extend `edit` tool schema and execution paths).
- Reuse: `src/main/file-service.ts` (`validateAndResolvePath`, `readTextFile`, `writeTextFile`).
- Token counting: `src/services/token-service-main.ts`.

MiniFileList
- Add: `src/components/agent-mini-file-list.tsx`.
- Integrate: `src/components/agent-panel.tsx` to mount the mini list and wire selection to current `pendingAttachments` Map.
- Token accuracy: call `requestFileContent` from `src/handlers/electron-handlers.ts` to fetch content + `tokenCount` lazily.


## Feasibility & Compatibility Assessment

Terminal (complexity: high)
- Architecture fit: clean separation (node-pty in main, xterm in renderer) matches current security posture; IPC pattern already used elsewhere.
- Breaking changes: none if terminal tool returns structured “not enabled” errors when `ENABLE_CODE_EXECUTION=false`.
- External deps: `node-pty` (native), `xterm` and addons (renderer). Packaging must add `asarUnpack` for `node-pty` binaries.
- Performance: streaming output can be large; implement ring buffer + cursor pagination; throttle IPC.
- Security: restrict cwd to `getAllowedWorkspacePaths()` roots; enforce `ENABLE_CODE_EXECUTION`; add heuristics to block obviously dangerous commands unless explicit approval.

edit.block/multi (complexity: medium)
- Architecture fit: extends existing `edit` tool; no new process model. Keep unified diff path for backward compatibility.
- Breaking changes: none (new `action` variants; existing `{ path, diff, apply }` preserved).
- Dependencies: none external.
- Performance: operate on in-memory file content with caps (bytes per file, max files); avoid scanning huge files.
- Security: reuse path validation; writes gated behind `ENABLE_FILE_WRITE` and approval mode (`always` requires approval).

MiniFileList (complexity: medium-low)
- Architecture fit: pure renderer, leverages existing `allFiles` prop and `AgentAttachmentList` state.
- Breaking changes: none; optional UI.
- Dependencies: none external (optional small CSS).
- Performance: cap unfiltered render to N items (e.g., 200) or add simple virtualization later; cache token counts.


## Risks & Challenges (with mitigations)

Terminal
- Native packaging friction (node-pty across mac/win/linux)
  - Mitigate by adding `asarUnpack` for `node-pty` in build, test with `npm run test-build:*`.
- Output flood causing UI jank
  - Ring buffer with pagination + truncation; IPC pushes size-limited chunks; backpressure friendly.
- Security regressions via shell
  - Require `ENABLE_CODE_EXECUTION=true`, rate-limit via `AgentSecurityManager`, restrict cwd to workspace, approval flow for risky patterns.
- Test complexity (pty in CI)
  - Mock `node-pty` for unit tests; add e2e smoke only where environment allows.

edit.block/multi
- Ambiguity in `searchPattern` semantics (regex vs literal)
  - Start with safe literal (exact substring) + optional `isRegex` flag; default off.
- Multi-file large previews
  - Clip previews, cap files and bytes; aggregate counts with `truncated` markers.

MiniFileList
- Large workspaces
  - Filter before render; cap list; defer deep tree rendering; cache token counts by path.
- Token counting latency
  - Show approximate by chars immediately; fetch precise on hover/selection via `requestFileContent`.


## Detailed Technical Design

Feature 1: Terminal Tool + UI

- Config gates
  - Must require `ENABLE_CODE_EXECUTION=true` (see `src/main/agent/config.ts`).
  - If false, tool returns `{ type: 'error', code: 'EXECUTION_DISABLED' }` and UI shows a banner.

- Main process (`src/main/terminal/terminal-manager.ts`, new)
  - API
    - `create(opts: { command?: string; args?: string[]; cwd?: string; cols?: number; rows?: number; env?: Record<string,string> }): { id, pid }` — resolve shell by platform if `command` absent.
    - `write(id, data)` → writes to pty.
    - `resize(id, cols, rows)`.
    - `kill(id)`.
    - `list()` → metadata array.
    - `getOutput(id, { fromCursor?: number, maxBytes?: number })` → `{ chunk, nextCursor, truncated }` from a per-session ring buffer (e.g., 2–4 MB).
  - Implementation notes
    - Use `node-pty` to spawn shell/command. Default shells: macOS/Linux: user shell (env `SHELL` fallback `/bin/bash` or `/bin/zsh`), Windows: `powershell.exe`.
    - Enforce cwd within `getAllowedWorkspacePaths()` roots (see `src/main/workspace-context.ts` and `src/main/file-service.ts::validateAndResolvePath`).
    - Maintain `Map<string, Session>` where `Session` contains `pty`, `buffer` (ring), `cursor`, `createdAt`, `name`, `cwd`, `cols`, `rows`.
    - Normalize incoming data to UTF-8 and strip unprintable control codes for stored buffer (xterm still renders raw stream live).

- IPC channels (main, add to `src/main/main.ts` and `src/main/ipc/schemas.ts`)
  - Handlers:
    - `'terminal:create'` → takes `{ command?, args?, cwd?, cols?, rows? }` returns `{ success, data: { id, pid } }`.
    - `'terminal:write'` → `{ id, data }` returns `{ success }`.
    - `'terminal:resize'` → `{ id, cols, rows }` returns `{ success }`.
    - `'terminal:kill'` → `{ id }` returns `{ success }`.
    - `'terminal:list'` → `{ success, data: SessionMeta[] }`.
    - `'terminal:output:get'` → `{ id, fromCursor?, maxBytes? }` returns `{ success, data: { chunk, nextCursor, truncated } }`.
  - Events:
    - `'terminal:output:${id}'` push events on new data to reduce polling when panel is visible. Fall back to pull.
  - Types: add Zod schemas under `src/main/ipc/schemas.ts` for each request shape.
  - Type notes:
    - Use branded `TerminalSessionId`, `Cursor`, `Bytes` on internal APIs after validation.
    - Expose template-literal channel type `type TerminalEventChannel = `terminal:output:${TerminalSessionId}`.
    - All IPC handlers return `Result<...>` with explicit error codes (e.g., `INVALID_PARAMS`, `EXECUTION_DISABLED`, `NOT_FOUND`).

- Agent tool (`src/main/agent/tools.ts`)
  - Replace stub with actions:
    - `start`: `{ command?: string; args?: string[]; cwd?: string; waitForReady?: boolean; readyPattern?: string } → { sessionId, pid }`.
    - `interact`: `{ sessionId, input } → { ok: true }`.
    - `output`: `{ sessionId, cursor?, maxBytes? } → { chunk, nextCursor, truncated }`.
    - `list`: `→ { sessions: SessionMeta[] }`.
    - `kill`: `{ sessionId } → { ok: true }`.
  - Enforcement: if `config.ENABLE_CODE_EXECUTION !== true`, return `EXECUTION_DISABLED` error; apply `security.allowToolExecution(sessionId)` for rate limiting; validate `cwd` under workspace roots.
  - Telemetry: use existing `onToolExecute` hook. Store `{ startedAt, durationMs }` already supported by `src/main/main.ts` persistence.

- Renderer UI (`src/components/terminal-panel.tsx`, new)
  - xterm setup with `FitAddon` and `WebLinksAddon`.
  - Tabs: “Agent Commands” (read-only view of agent sessions) and “User Terminal” (user-controlled session).
  - Resize: observe container size; call `'terminal:resize'`.
  - Stream: subscribe to `'terminal:output:${id}'`; request missed data on mount via `'terminal:output:get'`.
  - Approval UX: if an agent tool call includes a risky command (regex match e.g., `rm -rf`, `:(){ :|:& };:`, `format C:`), show approve/deny in Agent Panel (`AgentAlertBanner` pattern exists) and block tool execution until approved for this turn.
  - Type notes: Preload bridge exposes fully typed methods that mirror IPC handler request/response types; no `any` in renderer, use `as const` for action literals.

- Packaging
  - Add deps: `node-pty`, `xterm`, `xterm-addon-fit`, `xterm-addon-web-links`.
  - Update `build/scripts/fix-dependencies.js` to include `node_modules/node-pty/**` in `asarUnpack` list.
  - Validate with `npm run test-build:*` across targets.

- Tests
  - Unit: mock `node-pty` and verify TerminalManager state transitions, ring buffer, kill/resize.
  - Integration: exercise tool calls via `'agent:execute-tool'` for start→interact→output→kill.
  - UI: render panel, simulate streaming, ensure resize/scroll works; snapshot minimal DOM.


Feature 2: edit.block/multi

- API shape (backward compatible)
  - Keep existing unified diff path:
    - `{ path: string, diff: string, apply?: boolean }` → current behavior.
  - Add action variants (default preview):
    - `block`: `{ action: 'block', path, search: string, replacement: string, occurrence?: number, isRegex?: boolean, preview?: boolean }`.
    - `multi`: `{ action: 'multi', paths: string[], search: string, replacement: string, isRegex?: boolean, occurrencePolicy?: 'first'|'all'|'index', index?: number, preview?: boolean, maxFiles?: number }`.

- Implementation (`src/main/agent/tools.ts`)
  - Type contracts
    - Define `type EditAction = 'diff'|'block'|'multi'` as a literal union (pinned `as const`).
    - Return discriminated payloads: `{ type: 'preview', ... } | { type: 'applied', ... } | { type: 'error', code: '...' }`; use exhaustive `switch`.
    - Derive param types from Zod/jsonSchema and avoid `(params: any)`; introduce a local typed helper to wrap the AI SDK tool with typed execute.
  - Helper functions (module-local):
    - `findAllOccurrences(content, pattern, { isRegex }): Array<{ start: number; end: number }>`.
    - `applyOccurrence(content, occ, replacement): { content: string, delta: number }`.
    - `charDiff(a, b): Array<{ op: 'keep'|'add'|'del'; text: string }>` (small capped diff for preview only).
    - `contextLines(content, pos, n): { before: string[], after: string[] }`.
  - `block` path:
    - Validate path via `validateAndResolvePath`, `readTextFile`; skip likely binary.
    - Locate occurrences; if `occurrence` missing, pick first.
    - Build preview object:
      - `modified` (clipped), `occurrencesCount`, `replacedOccurrenceIndex`, `characterDiffs` (small), `contextLines`.
      - Token counts for original vs modified via `getMainTokenService()`.
    - Apply path (only when `ENABLE_FILE_WRITE === true` and approval mode is not `always`), else return `{ code: 'APPROVAL_NEEDED' }`.
  - `multi` path:
    - For each validated path (cap files and bytes): run first or policy-driven replacement using `block` logic; aggregate previews.
    - Return `{ files: Array<...>, totalReplacements, truncated }`. Stop on first hard error; include `partial: true` flag.

- Limits
  - Cap files (e.g., ≤200) and per-file bytes (align with `FILE_PROCESSING` constants).
  - Clip content to 20–40 KB in previews with `…(truncated)` markers.

- Tests
  - Unit: helpers (occurrence finding, diff building) and parameter validation.
  - Tool: preview payload shapes; invalid occurrence; binary skip; gating behavior for apply.


Feature 3: MiniFileList with Token Counters

- Component (`src/components/agent-mini-file-list.tsx`)
  - Props: `{ files: FileData[]; selected: string[]; onToggle(path: string): void; collapsed?: boolean }`.
  - Behavior: client-side search (substring); simple tree grouping by directory; show `N selected • M tokens` footer.
  - Token counts:
    - Immediate approximate by chars (using `TOKEN_COUNTING.CHARS_PER_TOKEN`).
    - On hover/selection, fetch precise via `requestFileContent(absPath)` from `src/handlers/electron-handlers.ts` to hydrate `tokenCount` cache.
  - Integrate above chat messages in `AgentPanel`; selection writes into `pendingAttachments` Map already used by the composer.

- Performance
  - Render cap for unfiltered view (e.g., first 200 items) to avoid heavy DOMs; show “+ more” indicator.
  - Consider follow-up: reuse `virtualized-file-list` if needed.

- Tests
  - UI interactions (search/select/deselect), token counter updates when content resolves, render cap.


## Implementation Order & Dependencies

Recommended order (value early, risk late):
1) edit.block/multi (server-only; quick to land; adds real capability without UI risk).
2) MiniFileList (renderer; improves context management and token clarity).
3) Terminal infra (main tool + IPC; larger surface; off by default when `ENABLE_CODE_EXECUTION=false`).
4) Terminal UI (xterm panel + approval UX).

Prerequisites
- Add preferences (if needed) for terminal defaults (e.g., shell path), though optional initially.
- Update `build/scripts/fix-dependencies.js` to include `node-pty` in `asarUnpack`.


## Step‑By‑Step Tasks (Expanded)

M1: edit.block/multi
1) Extend `edit` tool schema in `src/main/agent/tools.ts` to accept `{ action }` variants while preserving existing `{ path, diff, apply }` validation.
2) Implement helpers (local to `tools.ts`) and wire `block` and `multi` execution branches.
3) Add caps and clipping; reuse `getMainTokenService()` for token diffs.
4) Return structured errors for apply when gated: `{ code: 'WRITE_DISABLED'|'APPROVAL_NEEDED' }`.
5) Tests: add unit tests; extend existing tool tests to cover new actions.
6) Docs: update `docs/vercel-ai-sdk-agent-integration.md` “Edit Tool” with new shapes and examples.
7) Type Safety: ensure the tool action union is literal (`as const`), result is a discriminated union with exhaustive handling; derive types from schemas via `z.infer`.
8) Tests must meet TESTING.md guardrails: ≥2 assertions/test, ≤3 mocks/file, no `.skip`, behavior-first.

M2: MiniFileList
1) Add `src/components/agent-mini-file-list.tsx` with search, list, footer.
2) Integrate in `src/components/agent-panel.tsx` above messages; selection synced to `pendingAttachments`.
3) Add precise token fetch path using `requestFileContent` and maintain a `{ path → tokenCount }` cache.
4) Style minimally under `.agent-panel` scope; ensure keyboard nav basics.
5) Tests: search/select; token counts update; render cap behavior.
6) Type Safety: typed props and state; preload calls typed; avoid widening of constants and action names.
7) Tests: include negative/edge cases (empty lists, large lists), comply with assertion density/mocks limits.

M3: Terminal tool infra (main)
1) Deps: install `node-pty`, `xterm` (+ addons); update `asarUnpack` in `build/scripts/fix-dependencies.js` and packaging config as needed.
2) Implement `TerminalManager` (create/write/resize/kill/list/output) with ring buffer and workspace cwd constraints.
3) Add IPC handlers (`terminal:*`) and (optional) push events for output.
4) Implement terminal tool actions in `getAgentTools` honoring `ENABLE_CODE_EXECUTION` and rate limits.
5) Tests: manager unit tests + tool integration tests (mock pty).
6) Type Safety: branded ids/cursors, typed IPC handler params/results via `z.infer`, explicit `Result<...>` returns, exhaustive `switch`.
7) Tests: include concurrency cases, rate-limit denials, invalid params rejected by schemas; adhere to TESTING.md quality metrics.

M4: Terminal UI + approval UX
1) Implement `src/components/terminal-panel.tsx` (xterm + tabs + fit) and add resize/persisted height.
2) Wire IPC: start/stop/list/output; paginate output for large sessions.
3) Approval banner in Agent Panel for risky agent-initiated commands; remember approval per turn/session.
4) Tests: render, stream, resize, tabs.
5) Type Safety: preload bridge type-only exports; event channel string as template literal type; `as const` on action literals.
6) Tests: avoid snapshot overuse; assert behavior (output appears, pagination works, approval banner blocks until allowed); meet assertion density.


### Terminal IPC Schemas Scaffold (doc-only; no code changes here)

Add the following Zod schemas to `src/main/ipc/schemas.ts` to type IPC calls for the terminal feature. Include branded domain types and Result type for handler returns:

```ts
// Domain brands (types only)
type Brand<T, B extends string> = T & { readonly __brand: B };
export type TerminalSessionId = Brand<string, 'TerminalSessionId'>;
export type Cursor = Brand<number, 'Cursor'>;
export type Bytes = Brand<number, 'Bytes'>;

// Generic Result type for IPC returns
export type Ok<T> = { ok: true; value: T };
export type Err<E extends string> = { ok: false; error: E };
export type Result<T, E extends string> = Ok<T> | Err<E>;

// Terminal IPC schemas
export const TerminalCreateSchema = z.object({
  command: z.string().optional(),
  args: z.array(z.string()).optional(),
  cwd: z.string().optional(),
  cols: z.number().int().positive().optional(),
  rows: z.number().int().positive().optional(),
});

export const TerminalWriteSchema = z.object({
  id: z.string().min(1),
  data: z.string().min(1),
});

export const TerminalResizeSchema = z.object({
  id: z.string().min(1),
  cols: z.number().int().positive(),
  rows: z.number().int().positive(),
});

export const TerminalKillSchema = z.object({ id: z.string().min(1) });

export const TerminalListSchema = z.object({});

export const TerminalOutputGetSchema = z.object({
  id: z.string().min(1),
  fromCursor: z.number().int().nonnegative().optional(),
  maxBytes: z.number().int().positive().optional(),
});

export type TerminalCreateType = z.infer<typeof TerminalCreateSchema>;
export type TerminalWriteType = z.infer<typeof TerminalWriteSchema>;
export type TerminalResizeType = z.infer<typeof TerminalResizeSchema>;
export type TerminalKillType = z.infer<typeof TerminalKillSchema>;
export type TerminalListType = z.infer<typeof TerminalListSchema>;
export type TerminalOutputGetType = z.infer<typeof TerminalOutputGetSchema>;

// Typed event channel (renderer)
export type TerminalEventChannel = `terminal:output:${TerminalSessionId}`;
```

Main process wiring in `src/main/main.ts` (handlers only; pseudocode for placement):

```ts
// Inside createWindow/init section where other ipcMain.handle calls are registered
ipcMain.handle('terminal:create', async (_e, params) => {
  const p = TerminalCreateSchema.safeParse(params || {});
  if (!p.success) return { ok: false, error: 'INVALID_PARAMS' } satisfies Err<'INVALID_PARAMS'>;
  // Resolve cwd under workspace roots; enforce ENABLE_CODE_EXECUTION
  const { id, pid } = terminalManager.create(p.data);
  return { ok: true, value: { id, pid } } satisfies Ok<{ id: TerminalSessionId; pid: number }>;
});

ipcMain.handle('terminal:write', async (_e, params) => {
  const p = TerminalWriteSchema.safeParse(params || {});
  if (!p.success) return { ok: false, error: 'INVALID_PARAMS' } as const;
  terminalManager.write(p.data.id, p.data.data);
  return { ok: true, value: null } as const;
});

ipcMain.handle('terminal:resize', async (_e, params) => {
  const p = TerminalResizeSchema.safeParse(params || {});
  if (!p.success) return { ok: false, error: 'INVALID_PARAMS' } as const;
  terminalManager.resize(p.data.id, p.data.cols, p.data.rows);
  return { ok: true, value: null } as const;
});

ipcMain.handle('terminal:kill', async (_e, params) => {
  const p = TerminalKillSchema.safeParse(params || {});
  if (!p.success) return { ok: false, error: 'INVALID_PARAMS' } as const;
  terminalManager.kill(p.data.id);
  return { ok: true, value: null } as const;
});

ipcMain.handle('terminal:list', async () => ({ ok: true, value: terminalManager.list() } as const));

ipcMain.handle('terminal:output:get', async (_e, params) => {
  const p = TerminalOutputGetSchema.safeParse(params || {});
  if (!p.success) return { ok: false, error: 'INVALID_PARAMS' } as const;
  const out = terminalManager.getOutput(p.data.id, { fromCursor: p.data.fromCursor, maxBytes: p.data.maxBytes });
  return { ok: true, value: out } as const;
});

// Optional: push-based streaming when terminal panel is focused
terminalManager.on('data', (id, chunk) => {
  try { webContents?.send?.(`terminal:output:${id}` as TerminalEventChannel, { chunk } as const); } catch { /* noop */ }
});
```

Renderer usage (examples):
- Create: `ipcRenderer.invoke('terminal:create', { command, args, cwd, cols, rows } satisfies TerminalCreateType) -> Promise<Result<{ id: TerminalSessionId; pid: number}, 'INVALID_PARAMS'|'EXECUTION_DISABLED'>>`
- Write: `ipcRenderer.invoke('terminal:write', { id, data } satisfies TerminalWriteType) -> Promise<Result<null,'INVALID_PARAMS'|'NOT_FOUND'>>`
- Resize: `ipcRenderer.invoke('terminal:resize', { id, cols, rows } satisfies TerminalResizeType)`
- Output (paged): `ipcRenderer.invoke('terminal:output:get', { id, fromCursor, maxBytes } satisfies TerminalOutputGetType)`
- Subscribe: `ipcRenderer.on(channel as TerminalEventChannel, handler)`



## Security, Performance, Observability

- Security
  - Paths: all file and cwd validations via `validateAndResolvePath()` and `getAllowedWorkspacePaths()`.
- Gates: `ENABLE_FILE_WRITE`, `ENABLE_CODE_EXECUTION`, and approval mode enforced in tools; UI communicates when blocked.
  - Rate limits: keep `AgentSecurityManager.allowToolExecution(sessionId)` checks across tools.
  - Terminal command heuristics: explicit denylist and approval flow for risky patterns.

- Performance
  - Terminal: ring buffer, chunked IPC, optional debounce on push events.
  - Edit multi: caps on file count and per-file bytes; clip previews.
  - MiniFileList: render cap + cached token counts; avoid unnecessary re-renders.

- Observability
  - Reuse `onToolExecute` telemetry path; ensure terminal tool populates `startedAt`/`durationMs` with session metadata.
  - Persist usage already supported via `api-route-handlers.ts` `onFinish`.


## Acceptance Criteria

- edit.block
  - Returns `{ type: 'preview', path, occurrencesCount, replacedOccurrenceIndex, characterDiffs[], contextLines, tokenCounts }`.
- Apply path returns `{ type: 'applied', path, bytes }` only when gates allow; otherwise `{ code: 'APPROVAL_NEEDED' }`.
- edit.multi
  - Returns `{ files: [...previews], totalReplacements, truncated? }` with clipped content markers.
- MiniFileList
  - Visible above Agent messages; search filters client-side; selecting files updates `pendingAttachments`; footer token total updates as precise counts arrive.
- Terminal
  - Tool supports `start|interact|output|list|kill` with gating; `TerminalPanel` streams output live; approval UX blocks risky commands until allowed.


## Effort Estimates (T‑shirt; calendar days)

- edit.block/multi: M (2–3 days incl. tests)
- MiniFileList: M (2–3 days incl. tests)
- Terminal infra (main + tool + IPC): L (4–6 days incl. tests + packaging)
- Terminal UI: M/L (3–4 days incl. tests)


## Open Questions / Follow‑ups

- Approval persistence: per-turn ephemeral only or persist per-session until revoked?
- Terminal retention: GC policy for inactive sessions (consider TTL + manual close from UI).
- Search pattern semantics: start literal-only with optional `isRegex`; document carefully in `docs/vercel-ai-sdk-agent-integration.md`.
- Virtualization for MiniFileList: reuse `virtualized-file-list` if necessary for very large repos.


## Appendix: Example Payloads

- edit.block (preview)
```
{
  "action": "block",
  "path": "/abs/src/utils/example.ts",
  "search": "TODO",
  "replacement": "",
  "occurrence": 1,
  "preview": true
}
```

- terminal.start
```
{
  "action": "start",
  "command": "npm",
  "args": ["test"],
  "cwd": "/abs/workspace"
}
```

- mini file list footer calculation (renderer)
```
totalTokens = approxFromComposer + sum(preciseTokenCache[path] ?? approxFromChars(path))
```


## PR Checklist (Enforcement)

Use this checklist on PRs that implement items from this plan.

- Type Safety
  - No `any` or widened literals; `as const`/`satisfies` used for action enums, policies, constants, channels.
  - All new boundaries (IPC, tools) validate input via Zod/jsonSchema and derive types with `z.infer`.
  - Branded domain types applied internally after validation (SessionId, TerminalSessionId, PathAbsolute, Cursor, Bytes).
  - Discriminated unions for actions/results with exhaustive `switch` + `never` checks.
  - Preload/IPC bridge methods and event channels are fully typed (template-literal channels for terminal).

- Security & Gating
- Edit apply path respects `ENABLE_FILE_WRITE` and approval mode; returns structured errors when blocked.
  - Terminal requires `ENABLE_CODE_EXECUTION`; cwd validated under allowed roots; risky command heuristics + approval UX hooked.
  - Rate limiting enforced with `AgentSecurityManager`.

- Packaging & Env
  - If terminal code included: `node-pty` listed, `asarUnpack` updated to include `node_modules/node-pty/**`.
  - Test-build scripts run for target OSes where applicable.

- Tests (per TESTING.md)
  - ≥2 assertions/test; ≤3 mocks/test file; no `.skip`/`.todo`; snapshots optional and minimal.
  - Behavior-focused tests cover: happy path, negative/validation errors, edge cases, and concurrency where relevant.
  - Terminal: ring buffer pagination, multi-session isolation, rate-limit denials, invalid params; platform-specific shell selection logic covered by unit/integration where feasible.
  - Observability: tool execution telemetry persisted (startedAt/duration), usage rows updated post-stream.
