# PasteFlow — Top 3 High‑Impact Improvements (2025-09-06)

## Pre-Action Checklist
- Confirm audit findings and affected files/lines.
- Validate DB schema for `usage_summary` insert shape.
- Identify low-risk code points for retries and telemetry hooks.
- Reuse existing tools/utilities (ripgrep, DB helpers) where possible.
- Keep changes minimal; add null-safe handling and logs.

## Overview
- Focus: harden MVP reliability/observability and unblock agent utility.
- Selection criteria: high impact, low risk, 2–4 hour scope, measurable outcome.
- Sources: implementation-audit-report-2025-09-06.md and current code references.

---

## 1) Persist SDK Finish Telemetry (Usage Tokens + Latency Capture)
- Title: Persist SDK Finish Telemetry (Usage Tokens + Latency Capture)
- Description: Capture token usage from Vercel AI SDK `streamText` finish event and persist to `usage_summary`; also capture end-to-end latency server-side (DB or logs).
- Rationale: Improves cost/usage tracking and troubleshooting; addresses a Major gap flagged in the audit. Impact areas: reliability, observability, maintainability.

- Technical Implementation Steps:
  - In `src/main/api-route-handlers.ts` within `APIRouteHandlers.handleChat` (starts at `src/main/api-route-handlers.ts:262`), record a `start = Date.now()` just before calling `streamText` to compute latency.
  - Pass `onFinish` to `streamText` and persist usage:
    - Extract `usage` fields (`inputTokens`, `outputTokens`, `totalTokens`) if present.
    - Call `this.db.insertUsageSummary(sessionId, inputTokens ?? null, outputTokens ?? null, totalTokens ?? null)`.
    - Null-safe handling for providers that don’t return usage.
  - Latency capture (align with current architecture):
    - Headers cannot be reliably modified after streaming begins, so avoid latency response headers.
    - Option A (preferred, if allowed): add a nullable `latency_ms INTEGER` column to `usage_summary` and persist `Date.now() - start` inside `onFinish`.
    - Option B (no schema change): log latency in development mode (`console.debug`) and/or add a separate lightweight `agent_request_metrics` table later.
  - Remove the existing placeholder `insertUsageSummary(sessionId, null, null, null)` writes that occur after piping in `handleChat` (both the main path and the toolless fallback) and rely on `onFinish` to insert a single accurate row per request.
  - Optional: add dev log line with model/provider and token counts when `NODE_ENV=development`.

- Expected Outcomes:
  - Each successful streamed chat records non-null token counts when provider supports usage.
  - Latency captured server-side (persisted if Option A is adopted; otherwise visible in dev logs).

- Success Metrics:
  - ≥ 90% of `usage_summary` rows for successful sessions have non-null `total_tokens` within 24h of deployment.
  - If Option A: ≥ 90% of new rows have non-null `latency_ms`.
  - If Option B: Dev logs show latency for ≥ 95% of requests in local sessions.

- Dependencies:
  - DB insert prepared statement exists: `insertUsageSummary(sessionId, input, output, total)` in `src/main/db/database-implementation.ts:410-419`.
  - Schema currently has no latency column; Option A requires a one-time `ALTER TABLE usage_summary ADD COLUMN latency_ms INTEGER` migration.
  - Provider usage fields availability varies; implementation must be null-safe. Not blocked.

- Estimated Effort Breakdown:
  - Wire `onFinish` and DB insert: 0.8h
  - Header + dev logging + cleanup placeholders: 0.5h
  - Local verification and quick test pass: 0.5h
  - Total: 1.8h (Option A +0.3–0.5h for migration)

---

## 2) Server-side 429 Retry with Exponential Backoff (+ Jitter, Retry-After)
- Title: Server-side 429 Retry with Exponential Backoff (+ Jitter, Retry-After)
- Description: Wrap the `streamText` call with a small retry routine for rate-limited/provider-transient errors, respecting `Retry-After` when present, with capped attempts and jitter.
- Rationale: Reduces user-facing failures and improves resilience during transient rate limits. Impact areas: reliability, UX.

- Technical Implementation Steps:
  - Add a small utility function `withRateLimitRetries` in `src/main/utils/retry.ts`:
    - Signature: `async function withRateLimitRetries<T>(fn: () => Promise<T>, opts?: { attempts?: number; baseMs?: number; maxMs?: number }): Promise<T>`.
    - On failure, use `getStatusFromAIError(err)` (available at `src/main/api-route-handlers.ts:1039`) to detect 429/5xx.
    - Parse `Retry-After` if available from `(err as any).response?.headers?.get?.('retry-after')` or `(err as any).headers?.['retry-after']`; fallback to `baseMs * 2^retry + randomJitter` with cap `maxMs`.
    - Limit attempts to 3; rethrow last error.
  - In `src/main/api-route-handlers.ts` `APIRouteHandlers.handleChat`, wrap the `streamText` creation + piping inside a single attempt closure:
    - Important: Only retry if the attempt fails before any bytes are written; if piping begins, do not retry mid-stream (guard with `if (res.headersSent) throw` to skip retry).
    - Pseudocode per attempt: create `AbortController`, call `streamText`, then `pipeUIMessageStreamToResponse`; if error thrown before piping, let wrapper retry.
  - Preserve current tool-schema fallback path; apply the same retry policy to both tool-enabled and toolless paths.
  - Log retries in development: `console.warn('[AI] retrying after 429', { attempt, delayMs })`.

- Expected Outcomes:
  - Fewer 429 error responses reach the renderer; smoother chat during short rate-limit bursts.

- Success Metrics:
  - ≥ 50% reduction in 429 responses surfaced to UI across typical dev sessions (compare counts before/after in logs over a day).
  - Average retry count ≤ 1 per affected request (verify in logs).

- Dependencies:
  - None blocking; uses existing error helpers and standard headers.

- Estimated Effort Breakdown:
  - Implement utility + tests: 0.8h
  - Integrate in handler (both paths) + logging: 0.7h
  - Local verification (simulate 429) + polish: 0.5h
  - Total: 2.0h

---

## 3) Extend Consolidated Context Tool: `expand` and `search`
- Title: Extend Consolidated Context Tool: `expand` and `search`
- Description: Add `context.expand` (load file contents or line ranges) and `context.search` (query workspace with ripgrep) to empower the agent to fetch relevant context on demand.
- Rationale: Directly improves agent success on repo-aware tasks by enabling self-service context gathering. Impact areas: functionality, reliability.

- Technical Implementation Steps:
  - Update `src/main/agent/tools.ts` context tool:
    - Change schema to accept `{ action: 'summary' | 'expand' | 'search', ... }`.
    - Implement `summary` path by preserving current behavior (file counts).
    - Implement `expand`:
      - Input: `{ files: Array<{ path: string; lines?: { start: number; end: number } }>, maxBytes?: number }`.
      - Validate/resolve each path via `validateAndResolvePath`; read with `readTextFile`; enforce `maxBytes` cap; return `{ path, content, bytes, tokenCount }` using `tokenService.countTokens`.
    - Implement `search`:
      - Input: `{ query: string; directory?: string; maxResults?: number }`.
      - Delegate to existing `runRipgrepJson` in `src/main/tools/ripgrep.ts`; map to compact result format: `{ files: [{ path, matches: [{ line, text }] }], totalMatches, truncated }`.
    - Ensure `deps?.onToolExecute('context', params, result, meta)` is called with duration meta, like other tools.
  - Register extended schema with `jsonSchema(...)` like existing tools to avoid zod pitfalls.
  - Add basic tests in `src/main/__tests__/tools.context.test.ts`:
    - Validates `summary` counts, `expand` for a small test file, and `search` query returns matches.
  - No renderer changes required; the tool is available to the agent immediately.
  - Note: This complements the existing standalone `search` tool; do not remove it, as agents may select either path depending on prompt structure.

- Expected Outcomes:
  - Agent can autonomously retrieve file contents/snippets and perform focused searches.

- Success Metrics:
  - `tool_executions` entries show successful `context.expand`/`context.search` calls in typical sessions.
  - Reduction in agent follow-up prompts asking for more context during test tasks (qualitative) and measurable increase in successful single-pass edits in dogfooding.

- Dependencies:
  - Reuses `validateAndResolvePath`, `readTextFile`, `getMainTokenService`, and `runRipgrepJson`. Not blocked.

- Estimated Effort Breakdown:
  - Schema + summary refactor: 0.5h
  - `expand` implementation + caps: 0.9h
  - `search` integration + formatting: 0.8h
  - Tests + verification: 0.6h
  - Total: 2.8h

---

## Notes
- Deferred items (Terminal tool/UI, edit.block/multi, MiniFileList + token counters) are valuable but exceed a tight 2–4h window or need product guidance. They remain good candidates for the next iteration after these reliability/observability wins land.
